{"ast": null, "code": "// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects{constructor(){this.audioContext=null;// Inicializace pouze pokud je podporováno\nif(typeof window!=='undefined'&&'AudioContext'in window){this.audioContext=new AudioContext();}}// Zvuk pro řezání pizzy\nplaySliceSound(){if(!this.audioContext)return;const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(800,this.audioContext.currentTime);oscillator.frequency.exponentialRampToValueAtTime(400,this.audioContext.currentTime+0.1);gainNode.gain.setValueAtTime(0.1,this.audioContext.currentTime);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+0.1);oscillator.start(this.audioContext.currentTime);oscillator.stop(this.audioContext.currentTime+0.1);}// Zvuk pro výběr dílku\nplaySelectSound(){if(!this.audioContext)return;const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(600,this.audioContext.currentTime);oscillator.type='sine';gainNode.gain.setValueAtTime(0.05,this.audioContext.currentTime);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+0.05);oscillator.start(this.audioContext.currentTime);oscillator.stop(this.audioContext.currentTime+0.05);}// Zvuk pro správnou odpověď\nplaySuccessSound(){if(!this.audioContext)return;const frequencies=[523,659,784];// C, E, G\nfrequencies.forEach((freq,index)=>{const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(freq,this.audioContext.currentTime+index*0.1);oscillator.type='sine';gainNode.gain.setValueAtTime(0.1,this.audioContext.currentTime+index*0.1);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+index*0.1+0.2);oscillator.start(this.audioContext.currentTime+index*0.1);oscillator.stop(this.audioContext.currentTime+index*0.1+0.2);});}// Zvuk pro špatnou odpověď\nplayErrorSound(){if(!this.audioContext)return;const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(200,this.audioContext.currentTime);oscillator.type='sawtooth';gainNode.gain.setValueAtTime(0.1,this.audioContext.currentTime);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+0.3);oscillator.start(this.audioContext.currentTime);oscillator.stop(this.audioContext.currentTime+0.3);}}export const soundEffects=new SoundEffects();", "map": {"version": 3, "names": ["SoundEffects", "constructor", "audioContext", "window", "AudioContext", "playSliceSound", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "playSelectSound", "type", "playSuccessSound", "frequencies", "for<PERSON>ach", "freq", "index", "playErrorSound", "soundEffects"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/SoundEffects.tsx"], "sourcesContent": ["// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects {\n  private audioContext: AudioContext | null = null;\n\n  constructor() {\n    // Inicializace pouze pokud je podporováno\n    if (typeof window !== 'undefined' && 'AudioContext' in window) {\n      this.audioContext = new AudioContext();\n    }\n  }\n\n  // Zvuk pro řezání pizzy\n  playSliceSound() {\n    if (!this.audioContext) return;\n    \n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    \n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    \n    oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);\n    oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);\n    \n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);\n    \n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.1);\n  }\n\n  // Zvuk pro výběr dílku\n  playSelectSound() {\n    if (!this.audioContext) return;\n    \n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    \n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    \n    oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime);\n    oscillator.type = 'sine';\n    \n    gainNode.gain.setValueAtTime(0.05, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.05);\n    \n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.05);\n  }\n\n  // Zvuk pro správnou odpověď\n  playSuccessSound() {\n    if (!this.audioContext) return;\n    \n    const frequencies = [523, 659, 784]; // C, E, G\n    \n    frequencies.forEach((freq, index) => {\n      const oscillator = this.audioContext!.createOscillator();\n      const gainNode = this.audioContext!.createGain();\n      \n      oscillator.connect(gainNode);\n      gainNode.connect(this.audioContext!.destination);\n      \n      oscillator.frequency.setValueAtTime(freq, this.audioContext!.currentTime + index * 0.1);\n      oscillator.type = 'sine';\n      \n      gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime + index * 0.1);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + index * 0.1 + 0.2);\n      \n      oscillator.start(this.audioContext!.currentTime + index * 0.1);\n      oscillator.stop(this.audioContext!.currentTime + index * 0.1 + 0.2);\n    });\n  }\n\n  // Zvuk pro špatnou odpověď\n  playErrorSound() {\n    if (!this.audioContext) return;\n    \n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    \n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    \n    oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);\n    oscillator.type = 'sawtooth';\n    \n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);\n    \n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.3);\n  }\n}\n\nexport const soundEffects = new SoundEffects();\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,YAAa,CAGxBC,WAAWA,CAAA,CAAG,MAFNC,YAAY,CAAwB,IAAI,CAG9C;AACA,GAAI,MAAO,CAAAC,MAAM,GAAK,WAAW,EAAI,cAAc,EAAI,CAAAA,MAAM,CAAE,CAC7D,IAAI,CAACD,YAAY,CAAG,GAAI,CAAAE,YAAY,CAAC,CAAC,CACxC,CACF,CAEA;AACAC,cAAcA,CAAA,CAAG,CACf,GAAI,CAAC,IAAI,CAACH,YAAY,CAAE,OAExB,KAAM,CAAAI,UAAU,CAAG,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,CAAC,CACvD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACN,YAAY,CAACO,UAAU,CAAC,CAAC,CAE/CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAACS,WAAW,CAAC,CAE/CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,CAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CACvER,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,CAAE,IAAI,CAACb,YAAY,CAACY,WAAW,CAAG,GAAG,CAAC,CAE3FN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,CAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CAChEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACb,YAAY,CAACY,WAAW,CAAG,GAAG,CAAC,CAErFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAACY,WAAW,CAAC,CAC/CR,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAACY,WAAW,CAAG,GAAG,CAAC,CACtD,CAEA;AACAK,eAAeA,CAAA,CAAG,CAChB,GAAI,CAAC,IAAI,CAACjB,YAAY,CAAE,OAExB,KAAM,CAAAI,UAAU,CAAG,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,CAAC,CACvD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACN,YAAY,CAACO,UAAU,CAAC,CAAC,CAE/CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAACS,WAAW,CAAC,CAE/CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,CAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CACvER,UAAU,CAACc,IAAI,CAAG,MAAM,CAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,IAAI,CAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CACjEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACb,YAAY,CAACY,WAAW,CAAG,IAAI,CAAC,CAEtFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAACY,WAAW,CAAC,CAC/CR,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAACY,WAAW,CAAG,IAAI,CAAC,CACvD,CAEA;AACAO,gBAAgBA,CAAA,CAAG,CACjB,GAAI,CAAC,IAAI,CAACnB,YAAY,CAAE,OAExB,KAAM,CAAAoB,WAAW,CAAG,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAAE;AAErCA,WAAW,CAACC,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACnC,KAAM,CAAAnB,UAAU,CAAG,IAAI,CAACJ,YAAY,CAAEK,gBAAgB,CAAC,CAAC,CACxD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACN,YAAY,CAAEO,UAAU,CAAC,CAAC,CAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAAES,WAAW,CAAC,CAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAACW,IAAI,CAAE,IAAI,CAACtB,YAAY,CAAEY,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAC,CACvFnB,UAAU,CAACc,IAAI,CAAG,MAAM,CAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,CAAE,IAAI,CAACX,YAAY,CAAEY,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAC,CAC/EjB,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACb,YAAY,CAAEY,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAG,GAAG,CAAC,CAEpGnB,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAAEY,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAC,CAC9DnB,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAAEY,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAG,GAAG,CAAC,CACrE,CAAC,CAAC,CACJ,CAEA;AACAC,cAAcA,CAAA,CAAG,CACf,GAAI,CAAC,IAAI,CAACxB,YAAY,CAAE,OAExB,KAAM,CAAAI,UAAU,CAAG,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,CAAC,CACvD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACN,YAAY,CAACO,UAAU,CAAC,CAAC,CAE/CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAACS,WAAW,CAAC,CAE/CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,CAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CACvER,UAAU,CAACc,IAAI,CAAG,UAAU,CAE5BZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,CAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CAChEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACb,YAAY,CAACY,WAAW,CAAG,GAAG,CAAC,CAErFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAACY,WAAW,CAAC,CAC/CR,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAACY,WAAW,CAAG,GAAG,CAAC,CACtD,CACF,CAEA,MAAO,MAAM,CAAAa,YAAY,CAAG,GAAI,CAAA3B,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}