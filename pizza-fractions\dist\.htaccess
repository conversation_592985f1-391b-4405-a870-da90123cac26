# Pizza Fractions - Apache konfigurace (Verze 2.2 - Optimalizované UI)

# Povolení GZIP komprese pro lepší výkon
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Nastavení cache pro statické soubory
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
</IfModule>

# Bezpečnostní hlavičky
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# MIME typy pro správné načítání souborů
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/png .png
    AddType image/x-icon .ico
    AddType application/json .json
</IfModule>

# Přesměrování pro SPA (pouze pokud soubor neexistuje)
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    
    # Pokud je požadován existující soubor nebo složka, nech ho projít
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]
    
    # Jinak přesměruj na index.html
    RewriteRule ^ index.html [L]
</IfModule>
