# 🍕 Pizza Fractions - Nasazení na webhosting

## 📁 O<PERSON>ah složky /dist

Tato složka obsahuje všechny soubory potřebné pro nasazení hry Pizza Fractions na webhosting:

### Hlavní soubory:
- `index.html` - Hlavní HTML stránka aplikace (s relativními cestami)
- `pizzabaker.png` - Obrázek pekaře v záhlaví
- `favicon.ico` - Ikona webu
- `manifest.json` - PWA manifest
- `robots.txt` - Soubor pro vyhledávače

### Složka static/:
- `static/css/` - Optimalizované CSS styly
- `static/js/` - Optimalizovaný JavaScript kód

## 🚀 Nasazení na webhosting

### Krok 1: Upload souborů
1. Nahrajte **všechny soubory a složky** z této `/dist` složky do kořenového adresáře vašeho webhostingu
2. Zachovejte strukturu slo<PERSON>ek (zejména slož<PERSON> `static/`)

### Krok 2: Konfigurace serveru
- <PERSON><PERSON><PERSON><PERSON><PERSON> se, že server podporuje statické soubory
- Hlavn<PERSON> soubor: `index.html`
- Aplikace funguje jako Single Page Application (SPA)

### Krok 3: Testování
1. Otevřete webovou stránku v prohlížeči
2. Zkontrolujte, že se hra načte správně
3. Otestujte všechny funkce (krájení, výběr dílků, zvuky)

## ✅ VYLEPŠENO - Optimalizované rozhraní (Verze 2.2)

**Verze 2.2** - Vylepšené uživatelské rozhraní:
- ✅ **Úspora vertikálního místa**: Všechna tlačítka se zobrazují na stejném místě
- ✅ **Plynulý workflow**: 
  - Fáze krájení: "Krájet pizzu" + "Nakrájeno"
  - Fáze výběru: "Potvrdit odpověď"
  - Fáze dokončení: "Další kolo"
- ✅ Všechny cesty zůstávají relativní
- ✅ Žádné 404 chyby při načítání

### Workflow tlačítek v v2.2:
1. **Krájení**: Zobrazí se "Krájet pizzu" a "Nakrájeno"
2. **Po kliknutí "Nakrájeno"**: Tlačítka zmizí, zobrazí se "Potvrdit odpověď"
3. **Po potvrzení**: Zobrazí se "Další kolo"
4. **Po kliknutí "Další kolo"**: Vrátí se tlačítka pro krájení

## 🔧 Technické požadavky

### Server:
- Podpora statických souborů (HTML, CSS, JS, PNG)
- Žádné speciální požadavky na backend
- Funguje na jakémkoliv webhostingu (Apache, Nginx, atd.)
- **Funguje i v podadresářích** (např. example.com/games/pizza/)

### Prohlížeč:
- Moderní prohlížeč s podporou ES6+
- JavaScript musí být povolen
- Doporučeno: Chrome, Firefox, Safari, Edge

## 📱 Responsivní design

Hra je optimalizována pro:
- ✅ Desktop počítače
- ✅ Tablety
- ✅ Mobilní telefony
- ✅ Interaktivní projektory ve třídách
- ✅ **Úspora místa na malých obrazovkách**

## 🎵 Zvukové efekty

- Automaticky se aktivují při první interakci uživatele
- Fungují ve všech moderních prohlížečích
- Žádné externí audio soubory nejsou potřeba
- ✅ Opraveno: Zvuk funguje už od prvního kliknutí

## 📊 Velikost aplikace

- Celková velikost: ~65 kB (gzipped)
- Rychlé načítání i na pomalém připojení
- Optimalizováno pro výkon
- ✅ Menší CSS díky optimalizaci (v2.2)

## 🆘 Řešení problémů

### Hra se nenačte:
1. Zkontrolujte, že jsou všechny soubory nahrané
2. Ověřte, že je zachována struktura složek
3. Zkontrolujte konzoli prohlížeče (F12) pro chyby

### 404 chyby (OPRAVENO):
- ✅ Verze 2.2 používá relativní cesty pro VŠE
- ✅ Obrázek pizzabaker.png se načítá správně
- ✅ Žádné problémy s načítáním souborů

### Nefungují zvuky:
- Zvuky se aktivují až po první interakci uživatele
- Zkuste kliknout na tlačítko "Krájet pizzu"

### Problémy na mobilu:
- Zkontrolujte, že je povolený JavaScript
- Některé starší prohlížeče nemusí být podporované

## 📞 Podpora

Pro technickou podporu nebo dotazy kontaktujte vývojáře aplikace.

---
**Vytvořeno pomocí React.js | Optimalizováno pro vzdělávací účely | Verze 2.2 - Optimalizované UI**
