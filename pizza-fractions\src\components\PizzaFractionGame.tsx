import React, { useState, useEffect } from 'react';
import './PizzaFractionGame.css';
import PizzaSVG from './PizzaSVG';
import PizzaChef from './PizzaChef';
import { soundEffects } from './SoundEffects';

interface Fraction {
  numerator: number;
  denominator: number;
}

interface GameState {
  targetFraction: Fraction;
  currentFraction: Fraction;
  score: number;
  gamePhase: 'cutting' | 'selecting' | 'complete';
  selectedSlices: boolean[];
  totalSlices: number;
  feedback: string;
  showHint: boolean;
}

const PizzaFractionGame: React.FC = () => {
  const [gameState, setGameState] = useState<GameState>({
    targetFraction: { numerator: 1, denominator: 2 },
    currentFraction: { numerator: 0, denominator: 1 },
    score: 0,
    gamePhase: 'cutting',
    selectedSlices: [],
    totalSlices: 1,
    feedback: '',
    showHint: false
  });

  // Generuje náhodný zlomek menší ne<PERSON> 1, v<PERSON>dn<PERSON> pro děti
  const generateRandomFraction = (): Fraction => {
    // Začneme s jednodu<PERSON><PERSON><PERSON><PERSON>lo<PERSON> pro mladší děti
    const easyFractions = [
      { numerator: 1, denominator: 2 },
      { numerator: 1, denominator: 3 },
      { numerator: 2, denominator: 3 },
      { numerator: 1, denominator: 4 },
      { numerator: 3, denominator: 4 },
      { numerator: 1, denominator: 5 },
      { numerator: 2, denominator: 5 },
      { numerator: 3, denominator: 5 },
      { numerator: 4, denominator: 5 },
      { numerator: 1, denominator: 6 },
      { numerator: 5, denominator: 6 },
      { numerator: 1, denominator: 8 },
      { numerator: 3, denominator: 8 },
      { numerator: 5, denominator: 8 },
      { numerator: 7, denominator: 8 }
    ];

    return easyFractions[Math.floor(Math.random() * easyFractions.length)];
  };

  // Spustí novou hru
  const startNewRound = () => {
    const newFraction = generateRandomFraction();
    setGameState({
      targetFraction: newFraction,
      currentFraction: { numerator: 0, denominator: 1 },
      score: gameState.score,
      gamePhase: 'cutting',
      selectedSlices: [],
      totalSlices: 1,
      feedback: '',
      showHint: false
    });
  };

  // Resetuje celou hru
  const resetGame = () => {
    const newFraction = generateRandomFraction();
    setGameState({
      targetFraction: newFraction,
      currentFraction: { numerator: 0, denominator: 1 },
      score: 0,
      gamePhase: 'cutting',
      selectedSlices: [],
      totalSlices: 1,
      feedback: '',
      showHint: false
    });
  };

  // Zobrazí/skryje nápovědu
  const toggleHint = () => {
    setGameState(prev => ({ ...prev, showHint: !prev.showHint }));
  };

  // Řezání pizzy (zvyšuje počet dílků)
  const cutPizza = () => {
    if (gameState.gamePhase === 'cutting') {
      soundEffects.playSliceSound();
      const newTotalSlices = gameState.totalSlices + 1;
      setGameState(prev => ({
        ...prev,
        totalSlices: newTotalSlices,
        currentFraction: { ...prev.currentFraction, denominator: newTotalSlices },
        selectedSlices: new Array(newTotalSlices).fill(false)
      }));
    }
  };

  // Výběr/zrušení výběru dílku pizzy
  const toggleSlice = (index: number) => {
    if (gameState.gamePhase === 'cutting') {
      setGameState(prev => ({ ...prev, gamePhase: 'selecting' }));
    }

    if (gameState.gamePhase === 'selecting') {
      soundEffects.playSelectSound();
      const newSelectedSlices = [...gameState.selectedSlices];
      newSelectedSlices[index] = !newSelectedSlices[index];
      const selectedCount = newSelectedSlices.filter(Boolean).length;

      setGameState(prev => ({
        ...prev,
        selectedSlices: newSelectedSlices,
        currentFraction: { numerator: selectedCount, denominator: prev.totalSlices }
      }));
    }
  };

  // Kontrola správnosti odpovědi
  const checkAnswer = () => {
    const { targetFraction, currentFraction } = gameState;
    
    // Zjednodušení zlomků pro porovnání
    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);
    
    const simplifyFraction = (frac: Fraction): Fraction => {
      const divisor = gcd(frac.numerator, frac.denominator);
      return {
        numerator: frac.numerator / divisor,
        denominator: frac.denominator / divisor
      };
    };
    
    const simplifiedTarget = simplifyFraction(targetFraction);
    const simplifiedCurrent = simplifyFraction(currentFraction);
    
    return simplifiedTarget.numerator === simplifiedCurrent.numerator && 
           simplifiedTarget.denominator === simplifiedCurrent.denominator;
  };

  // Potvrzení odpovědi
  const submitAnswer = () => {
    if (gameState.gamePhase === 'selecting') {
      const isCorrect = checkAnswer();
      if (isCorrect) {
        soundEffects.playSuccessSound();
        setGameState(prev => ({
          ...prev,
          score: prev.score + 1,
          gamePhase: 'complete',
          feedback: '🎉 Výborně! Správná odpověď!'
        }));
      } else {
        soundEffects.playErrorSound();
        // Špatná odpověď - resetuj výběr
        setGameState(prev => ({
          ...prev,
          selectedSlices: new Array(prev.totalSlices).fill(false),
          currentFraction: { numerator: 0, denominator: prev.totalSlices },
          feedback: '❌ Zkus to znovu! Zkontroluj si zlomek.'
        }));
      }
    }
  };

  // Inicializace první hry
  useEffect(() => {
    const newFraction = generateRandomFraction();
    setGameState({
      targetFraction: newFraction,
      currentFraction: { numerator: 0, denominator: 1 },
      score: 0,
      gamePhase: 'cutting',
      selectedSlices: [],
      totalSlices: 1,
      feedback: '',
      showHint: false
    });
  }, []);

  return (
    <div className="pizza-game">
      <header className="game-header">
        <h1>
          <PizzaChef />
          Pizza Zlomky
        </h1>
        <div className="game-info">
          <div className="score">Skóre: {gameState.score}</div>
          <div className="target-fraction">
            Cíl: {gameState.targetFraction.numerator}/{gameState.targetFraction.denominator}
          </div>
          <div className="current-fraction">
            Tvůj zlomek: {gameState.currentFraction.numerator}/{gameState.currentFraction.denominator}
          </div>
        </div>
        <div className="game-controls">
          <button className="hint-button" onClick={toggleHint}>
            💡 {gameState.showHint ? 'Skrýt nápovědu' : 'Zobrazit nápovědu'}
          </button>
          <button className="reset-button" onClick={resetGame}>
            🔄 Nová hra
          </button>
        </div>
      </header>

      <main className="game-content">
        <div className="instructions">
          {gameState.gamePhase === 'cutting' && (
            <p>🔪 Klikni na nůž pro řezání pizzy na více dílků!</p>
          )}
          {gameState.gamePhase === 'selecting' && (
            <p>👆 Klikni na dílky pizzy pro výběr správného množství!</p>
          )}
          {gameState.gamePhase === 'complete' && (
            <p>🎉 Výborně! Klikni na "Další kolo" pro pokračování.</p>
          )}
          {gameState.showHint && (
            <div className="hint">
              💡 <strong>Nápověda:</strong> Pro zlomek {gameState.targetFraction.numerator}/{gameState.targetFraction.denominator}
              potřebuješ rozdělit pizzu na {gameState.targetFraction.denominator} dílků
              a vybrat {gameState.targetFraction.numerator} z nich.
            </div>
          )}
          {gameState.feedback && (
            <div className={`feedback ${gameState.feedback.includes('❌') ? 'error' : 'success'}`}>
              {gameState.feedback}
            </div>
          )}
        </div>

        <div className="game-area">
          <div className="knife-section">
            <button 
              className="knife-button"
              onClick={cutPizza}
              disabled={gameState.gamePhase !== 'cutting'}
            >
              🔪 Řezat pizzu
            </button>
          </div>

          <div className="pizza-container">
            <PizzaSVG
              totalSlices={gameState.totalSlices}
              selectedSlices={gameState.selectedSlices}
              onSliceClick={toggleSlice}
            />
          </div>

          <div className="controls">
            {gameState.gamePhase === 'selecting' && (
              <button className="submit-button" onClick={submitAnswer}>
                ✅ Potvrdit odpověď
              </button>
            )}
            {gameState.gamePhase === 'complete' && (
              <button className="next-round-button" onClick={startNewRound}>
                ➡️ Další kolo
              </button>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default PizzaFractionGame;
