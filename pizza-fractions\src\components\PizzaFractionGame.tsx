import React, { useState, useEffect } from 'react';
import './PizzaFractionGame.css';
import PizzaSVG from './PizzaSVG';

interface Fraction {
  numerator: number;
  denominator: number;
}

interface GameState {
  targetFraction: Fraction;
  currentFraction: Fraction;
  score: number;
  gamePhase: 'cutting' | 'selecting' | 'complete';
  selectedSlices: boolean[];
  totalSlices: number;
  feedback: string;
}

const PizzaFractionGame: React.FC = () => {
  const [gameState, setGameState] = useState<GameState>({
    targetFraction: { numerator: 1, denominator: 2 },
    currentFraction: { numerator: 0, denominator: 1 },
    score: 0,
    gamePhase: 'cutting',
    selectedSlices: [],
    totalSlices: 1,
    feedback: ''
  });

  // Generuje náhodný zlomek menší než 1
  const generateRandomFraction = (): Fraction => {
    const denominators = [2, 3, 4, 5, 6, 8, 10, 12];
    const denominator = denominators[Math.floor(Math.random() * denominators.length)];
    const numerator = Math.floor(Math.random() * denominator) + 1;
    return { numerator, denominator };
  };

  // Spustí novou hru
  const startNewRound = () => {
    const newFraction = generateRandomFraction();
    setGameState({
      targetFraction: newFraction,
      currentFraction: { numerator: 0, denominator: 1 },
      score: gameState.score,
      gamePhase: 'cutting',
      selectedSlices: [],
      totalSlices: 1,
      feedback: ''
    });
  };

  // Řezání pizzy (zvyšuje počet dílků)
  const cutPizza = () => {
    if (gameState.gamePhase === 'cutting') {
      const newTotalSlices = gameState.totalSlices + 1;
      setGameState(prev => ({
        ...prev,
        totalSlices: newTotalSlices,
        currentFraction: { ...prev.currentFraction, denominator: newTotalSlices },
        selectedSlices: new Array(newTotalSlices).fill(false)
      }));
    }
  };

  // Výběr/zrušení výběru dílku pizzy
  const toggleSlice = (index: number) => {
    if (gameState.gamePhase === 'cutting') {
      setGameState(prev => ({ ...prev, gamePhase: 'selecting' }));
    }
    
    if (gameState.gamePhase === 'selecting') {
      const newSelectedSlices = [...gameState.selectedSlices];
      newSelectedSlices[index] = !newSelectedSlices[index];
      const selectedCount = newSelectedSlices.filter(Boolean).length;
      
      setGameState(prev => ({
        ...prev,
        selectedSlices: newSelectedSlices,
        currentFraction: { numerator: selectedCount, denominator: prev.totalSlices }
      }));
    }
  };

  // Kontrola správnosti odpovědi
  const checkAnswer = () => {
    const { targetFraction, currentFraction } = gameState;
    
    // Zjednodušení zlomků pro porovnání
    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);
    
    const simplifyFraction = (frac: Fraction): Fraction => {
      const divisor = gcd(frac.numerator, frac.denominator);
      return {
        numerator: frac.numerator / divisor,
        denominator: frac.denominator / divisor
      };
    };
    
    const simplifiedTarget = simplifyFraction(targetFraction);
    const simplifiedCurrent = simplifyFraction(currentFraction);
    
    return simplifiedTarget.numerator === simplifiedCurrent.numerator && 
           simplifiedTarget.denominator === simplifiedCurrent.denominator;
  };

  // Potvrzení odpovědi
  const submitAnswer = () => {
    if (gameState.gamePhase === 'selecting') {
      const isCorrect = checkAnswer();
      if (isCorrect) {
        setGameState(prev => ({
          ...prev,
          score: prev.score + 1,
          gamePhase: 'complete',
          feedback: '🎉 Výborně! Správná odpověď!'
        }));
      } else {
        // Špatná odpověď - resetuj výběr
        setGameState(prev => ({
          ...prev,
          selectedSlices: new Array(prev.totalSlices).fill(false),
          currentFraction: { numerator: 0, denominator: prev.totalSlices },
          feedback: '❌ Zkus to znovu! Zkontroluj si zlomek.'
        }));
      }
    }
  };

  // Inicializace první hry
  useEffect(() => {
    const newFraction = generateRandomFraction();
    setGameState({
      targetFraction: newFraction,
      currentFraction: { numerator: 0, denominator: 1 },
      score: 0,
      gamePhase: 'cutting',
      selectedSlices: [],
      totalSlices: 1,
      feedback: ''
    });
  }, []);

  return (
    <div className="pizza-game">
      <header className="game-header">
        <h1>🍕 Pizza Zlomky</h1>
        <div className="game-info">
          <div className="score">Skóre: {gameState.score}</div>
          <div className="target-fraction">
            Cíl: {gameState.targetFraction.numerator}/{gameState.targetFraction.denominator}
          </div>
          <div className="current-fraction">
            Tvůj zlomek: {gameState.currentFraction.numerator}/{gameState.currentFraction.denominator}
          </div>
        </div>
      </header>

      <main className="game-content">
        <div className="instructions">
          {gameState.gamePhase === 'cutting' && (
            <p>🔪 Klikni na nůž pro řezání pizzy na více dílků!</p>
          )}
          {gameState.gamePhase === 'selecting' && (
            <p>👆 Klikni na dílky pizzy pro výběr správného množství!</p>
          )}
          {gameState.gamePhase === 'complete' && (
            <p>🎉 Výborně! Klikni na "Další kolo" pro pokračování.</p>
          )}
          {gameState.feedback && (
            <div className={`feedback ${gameState.feedback.includes('❌') ? 'error' : 'success'}`}>
              {gameState.feedback}
            </div>
          )}
        </div>

        <div className="game-area">
          <div className="knife-section">
            <button 
              className="knife-button"
              onClick={cutPizza}
              disabled={gameState.gamePhase !== 'cutting'}
            >
              🔪 Řezat pizzu
            </button>
          </div>

          <div className="pizza-container">
            <PizzaSVG
              totalSlices={gameState.totalSlices}
              selectedSlices={gameState.selectedSlices}
              onSliceClick={toggleSlice}
            />
          </div>

          <div className="controls">
            {gameState.gamePhase === 'selecting' && (
              <button className="submit-button" onClick={submitAnswer}>
                ✅ Potvrdit odpověď
              </button>
            )}
            {gameState.gamePhase === 'complete' && (
              <button className="next-round-button" onClick={startNewRound}>
                ➡️ Další kolo
              </button>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default PizzaFractionGame;
