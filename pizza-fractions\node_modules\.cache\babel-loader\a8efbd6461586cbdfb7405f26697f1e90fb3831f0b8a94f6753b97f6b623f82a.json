{"ast": null, "code": "// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects {\n  constructor() {\n    this.audioContext = null;\n    // Inicializace pouze pokud je podporováno\n    if (typeof window !== 'undefined' && 'AudioContext' in window) {\n      this.audioContext = new AudioContext();\n    }\n  }\n\n  // Aktivuje AudioContext při první interakci\n  async ensureAudioContext() {\n    if (!this.audioContext) return false;\n    if (this.audioContext.state === 'suspended') {\n      try {\n        await this.audioContext.resume();\n      } catch (error) {\n        console.warn('Nepodařilo se aktivovat AudioContext:', error);\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // Zvuk pro řezání pizzy\n  async playSliceSound() {\n    if (!(await this.ensureAudioContext())) return;\n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);\n    oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);\n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);\n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.1);\n  }\n\n  // Zvuk pro výběr dílku\n  async playSelectSound() {\n    if (!(await this.ensureAudioContext())) return;\n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime);\n    oscillator.type = 'sine';\n    gainNode.gain.setValueAtTime(0.05, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.05);\n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.05);\n  }\n\n  // Zvuk pro správnou odpověď\n  async playSuccessSound() {\n    if (!(await this.ensureAudioContext())) return;\n    const frequencies = [523, 659, 784]; // C, E, G\n\n    frequencies.forEach((freq, index) => {\n      const oscillator = this.audioContext.createOscillator();\n      const gainNode = this.audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(this.audioContext.destination);\n      oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime + index * 0.1);\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime + index * 0.1);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + index * 0.1 + 0.2);\n      oscillator.start(this.audioContext.currentTime + index * 0.1);\n      oscillator.stop(this.audioContext.currentTime + index * 0.1 + 0.2);\n    });\n  }\n\n  // Zvuk pro špatnou odpověď\n  playErrorSound() {\n    if (!this.audioContext) return;\n\n    // Ujistíme se, že AudioContext je aktivní\n    if (this.audioContext.state === 'suspended') {\n      this.audioContext.resume();\n    }\n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);\n    oscillator.type = 'sawtooth';\n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);\n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.3);\n  }\n}\nexport const soundEffects = new SoundEffects();", "map": {"version": 3, "names": ["SoundEffects", "constructor", "audioContext", "window", "AudioContext", "ensureAudioContext", "state", "resume", "error", "console", "warn", "playSliceSound", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "playSelectSound", "type", "playSuccessSound", "frequencies", "for<PERSON>ach", "freq", "index", "playErrorSound", "soundEffects"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/SoundEffects.tsx"], "sourcesContent": ["// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects {\n  private audioContext: AudioContext | null = null;\n\n  constructor() {\n    // Inicializace pouze pokud je podporováno\n    if (typeof window !== 'undefined' && 'AudioContext' in window) {\n      this.audioContext = new AudioContext();\n    }\n  }\n\n  // Aktivuje AudioContext při první interakci\n  private async ensureAudioContext() {\n    if (!this.audioContext) return false;\n\n    if (this.audioContext.state === 'suspended') {\n      try {\n        await this.audioContext.resume();\n      } catch (error) {\n        console.warn('Nepodařilo se aktivovat AudioContext:', error);\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // Zvuk pro řezání pizzy\n  async playSliceSound() {\n    if (!(await this.ensureAudioContext())) return;\n\n    const oscillator = this.audioContext!.createOscillator();\n    const gainNode = this.audioContext!.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext!.destination);\n\n    oscillator.frequency.setValueAtTime(800, this.audioContext!.currentTime);\n    oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext!.currentTime + 0.1);\n\n    gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.1);\n\n    oscillator.start(this.audioContext!.currentTime);\n    oscillator.stop(this.audioContext!.currentTime + 0.1);\n  }\n\n  // Zvuk pro výběr dílku\n  async playSelectSound() {\n    if (!(await this.ensureAudioContext())) return;\n\n    const oscillator = this.audioContext!.createOscillator();\n    const gainNode = this.audioContext!.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext!.destination);\n\n    oscillator.frequency.setValueAtTime(600, this.audioContext!.currentTime);\n    oscillator.type = 'sine';\n\n    gainNode.gain.setValueAtTime(0.05, this.audioContext!.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.05);\n\n    oscillator.start(this.audioContext!.currentTime);\n    oscillator.stop(this.audioContext!.currentTime + 0.05);\n  }\n\n  // Zvuk pro správnou odpověď\n  async playSuccessSound() {\n    if (!(await this.ensureAudioContext())) return;\n\n    const frequencies = [523, 659, 784]; // C, E, G\n\n    frequencies.forEach((freq, index) => {\n      const oscillator = this.audioContext!.createOscillator();\n      const gainNode = this.audioContext!.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(this.audioContext!.destination);\n\n      oscillator.frequency.setValueAtTime(freq, this.audioContext!.currentTime + index * 0.1);\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime + index * 0.1);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + index * 0.1 + 0.2);\n\n      oscillator.start(this.audioContext!.currentTime + index * 0.1);\n      oscillator.stop(this.audioContext!.currentTime + index * 0.1 + 0.2);\n    });\n  }\n\n  // Zvuk pro špatnou odpověď\n  playErrorSound() {\n    if (!this.audioContext) return;\n\n    // Ujistíme se, že AudioContext je aktivní\n    if (this.audioContext.state === 'suspended') {\n      this.audioContext.resume();\n    }\n\n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n\n    oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);\n    oscillator.type = 'sawtooth';\n\n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);\n\n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.3);\n  }\n}\n\nexport const soundEffects = new SoundEffects();\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,YAAY,CAAC;EAGxBC,WAAWA,CAAA,EAAG;IAAA,KAFNC,YAAY,GAAwB,IAAI;IAG9C;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,cAAc,IAAIA,MAAM,EAAE;MAC7D,IAAI,CAACD,YAAY,GAAG,IAAIE,YAAY,CAAC,CAAC;IACxC;EACF;;EAEA;EACA,MAAcC,kBAAkBA,CAAA,EAAG;IACjC,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE,OAAO,KAAK;IAEpC,IAAI,IAAI,CAACA,YAAY,CAACI,KAAK,KAAK,WAAW,EAAE;MAC3C,IAAI;QACF,MAAM,IAAI,CAACJ,YAAY,CAACK,MAAM,CAAC,CAAC;MAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,uCAAuC,EAAEF,KAAK,CAAC;QAC5D,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;;EAEA;EACA,MAAMG,cAAcA,CAAA,EAAG;IACrB,IAAI,EAAE,MAAM,IAAI,CAACN,kBAAkB,CAAC,CAAC,CAAC,EAAE;IAExC,MAAMO,UAAU,GAAG,IAAI,CAACV,YAAY,CAAEW,gBAAgB,CAAC,CAAC;IACxD,MAAMC,QAAQ,GAAG,IAAI,CAACZ,YAAY,CAAEa,UAAU,CAAC,CAAC;IAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;IAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAAEe,WAAW,CAAC;IAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC;IACxER,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,EAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,GAAG,GAAG,CAAC;IAE5FN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC;IACjEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,GAAG,GAAG,CAAC;IAEtFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAAEkB,WAAW,CAAC;IAChDR,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAEkB,WAAW,GAAG,GAAG,CAAC;EACvD;;EAEA;EACA,MAAMK,eAAeA,CAAA,EAAG;IACtB,IAAI,EAAE,MAAM,IAAI,CAACpB,kBAAkB,CAAC,CAAC,CAAC,EAAE;IAExC,MAAMO,UAAU,GAAG,IAAI,CAACV,YAAY,CAAEW,gBAAgB,CAAC,CAAC;IACxD,MAAMC,QAAQ,GAAG,IAAI,CAACZ,YAAY,CAAEa,UAAU,CAAC,CAAC;IAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;IAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAAEe,WAAW,CAAC;IAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC;IACxER,UAAU,CAACc,IAAI,GAAG,MAAM;IAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,IAAI,EAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC;IAClEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,GAAG,IAAI,CAAC;IAEvFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAAEkB,WAAW,CAAC;IAChDR,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAEkB,WAAW,GAAG,IAAI,CAAC;EACxD;;EAEA;EACA,MAAMO,gBAAgBA,CAAA,EAAG;IACvB,IAAI,EAAE,MAAM,IAAI,CAACtB,kBAAkB,CAAC,CAAC,CAAC,EAAE;IAExC,MAAMuB,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;IAErCA,WAAW,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACnC,MAAMnB,UAAU,GAAG,IAAI,CAACV,YAAY,CAAEW,gBAAgB,CAAC,CAAC;MACxD,MAAMC,QAAQ,GAAG,IAAI,CAACZ,YAAY,CAAEa,UAAU,CAAC,CAAC;MAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAAEe,WAAW,CAAC;MAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAACW,IAAI,EAAE,IAAI,CAAC5B,YAAY,CAAEkB,WAAW,GAAGW,KAAK,GAAG,GAAG,CAAC;MACvFnB,UAAU,CAACc,IAAI,GAAG,MAAM;MAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,GAAGW,KAAK,GAAG,GAAG,CAAC;MAC/EjB,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,GAAGW,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;MAEpGnB,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAAEkB,WAAW,GAAGW,KAAK,GAAG,GAAG,CAAC;MAC9DnB,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAEkB,WAAW,GAAGW,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;IACrE,CAAC,CAAC;EACJ;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC9B,YAAY,EAAE;;IAExB;IACA,IAAI,IAAI,CAACA,YAAY,CAACI,KAAK,KAAK,WAAW,EAAE;MAC3C,IAAI,CAACJ,YAAY,CAACK,MAAM,CAAC,CAAC;IAC5B;IAEA,MAAMK,UAAU,GAAG,IAAI,CAACV,YAAY,CAACW,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG,IAAI,CAACZ,YAAY,CAACa,UAAU,CAAC,CAAC;IAE/CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;IAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAACe,WAAW,CAAC;IAE/CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;IACvER,UAAU,CAACc,IAAI,GAAG,UAAU;IAE5BZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAE,IAAI,CAACjB,YAAY,CAACkB,WAAW,CAAC;IAChEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACnB,YAAY,CAACkB,WAAW,GAAG,GAAG,CAAC;IAErFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAACkB,WAAW,CAAC;IAC/CR,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAACkB,WAAW,GAAG,GAAG,CAAC;EACtD;AACF;AAEA,OAAO,MAAMa,YAAY,GAAG,IAAIjC,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}