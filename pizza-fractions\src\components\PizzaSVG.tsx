import React from 'react';

interface PizzaSVGProps {
  totalSlices: number;
  selectedSlices: boolean[];
  onSliceClick: (index: number) => void;
}

const PizzaSVG: React.FC<PizzaSVGProps> = ({ totalSlices, selectedSlices, onSliceClick }) => {
  const centerX = 150;
  const centerY = 150;
  const radius = 130;

  const createSlicePath = (index: number) => {
    const anglePerSlice = (2 * Math.PI) / totalSlices;
    const startAngle = index * anglePerSlice - Math.PI / 2; // Start from top
    const endAngle = (index + 1) * anglePerSlice - Math.PI / 2;

    const x1 = centerX + radius * Math.cos(startAngle);
    const y1 = centerY + radius * Math.sin(startAngle);
    const x2 = centerX + radius * Math.cos(endAngle);
    const y2 = centerY + radius * Math.sin(endAngle);

    const largeArcFlag = anglePerSlice > Math.PI ? 1 : 0;

    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };

  return (
    <div className="pizza-svg-container">
      <svg width="300" height="300" viewBox="0 0 300 300">
        {/* Pizza base */}
        <circle
          cx={centerX}
          cy={centerY}
          r={radius + 8}
          fill="#d68910"
          stroke="#b7950b"
          strokeWidth="4"
        />
        <circle
          cx={centerX}
          cy={centerY}
          r={radius}
          fill="#f39c12"
        />
        
        {/* Pizza slices */}
        {Array.from({ length: totalSlices }, (_, index) => (
          <g key={index}>
            <path
              d={createSlicePath(index)}
              fill={selectedSlices[index] ? "#2ecc71" : "#f39c12"}
              stroke="#d68910"
              strokeWidth="2"
              className="pizza-slice-svg"
              onClick={() => onSliceClick(index)}
              style={{
                cursor: 'pointer',
                filter: selectedSlices[index] ? 'brightness(1.1) drop-shadow(0 0 10px rgba(46, 204, 113, 0.5))' : 'none',
                transition: 'all 0.3s ease'
              }}
            />
            
            {/* Pizza toppings */}
            {totalSlices <= 8 && (
              <g>
                <circle
                  cx={centerX + (radius * 0.4) * Math.cos((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2)}
                  cy={centerY + (radius * 0.4) * Math.sin((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2)}
                  r="4"
                  fill="#e74c3c"
                />
                <circle
                  cx={centerX + (radius * 0.6) * Math.cos((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2)}
                  cy={centerY + (radius * 0.6) * Math.sin((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2)}
                  r="3"
                  fill="#27ae60"
                />
                <circle
                  cx={centerX + (radius * 0.5) * Math.cos((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2)}
                  cy={centerY + (radius * 0.5) * Math.sin((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2)}
                  r="3"
                  fill="#f1c40f"
                />
              </g>
            )}
          </g>
        ))}
        
        {/* Slice dividers */}
        {Array.from({ length: totalSlices }, (_, index) => {
          const angle = index * (2 * Math.PI) / totalSlices - Math.PI / 2;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);
          
          return (
            <line
              key={`divider-${index}`}
              x1={centerX}
              y1={centerY}
              x2={x}
              y2={y}
              stroke="#d68910"
              strokeWidth="3"
            />
          );
        })}
      </svg>
    </div>
  );
};

export default PizzaSVG;
