@echo off
echo.
echo ========================================
echo   PIZZA FRACTIONS - Lokalni test v2.0
echo ========================================
echo.
echo OPRAVENO: Relativni cesty - zadne 404 chyby!
echo.
echo Spoustim lokalni server pro testovani...
echo.
echo Po spusteni otevrete v prohlizeci:
echo http://localhost:8000
echo.
echo Pro ukonceni stisknete Ctrl+C
echo.
echo ========================================
echo.

REM Spusti Python HTTP server pokud je dostupny
python -m http.server 8000 2>nul
if %errorlevel% neq 0 (
    echo Python neni dostupny, zkousim Node.js...
    npx serve -s . -p 8000 2>nul
    if %errorlevel% neq 0 (
        echo.
        echo CHYBA: Ani Python ani Node.js nejsou dostupne.
        echo.
        echo Pro testovani nahrajte soubory na webhosting
        echo nebo nainstalujte Python ci Node.js.
        echo.
        echo Alternativne: Otevrete index.html primo v prohlizeci
        echo (nektery funkce nemusí fungovat bez HTTP serveru)
        echo.
        pause
    )
)
