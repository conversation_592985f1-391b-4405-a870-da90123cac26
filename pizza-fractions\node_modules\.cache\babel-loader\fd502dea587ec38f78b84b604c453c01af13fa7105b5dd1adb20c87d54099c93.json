{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PizzaSVG=_ref=>{let{totalSlices,selectedSlices,onSliceClick}=_ref;const centerX=150;const centerY=150;const radius=130;const createSlicePath=index=>{const anglePerSlice=2*Math.PI/totalSlices;const startAngle=index*anglePerSlice-Math.PI/2;// Start from top\nconst endAngle=(index+1)*anglePerSlice-Math.PI/2;const x1=centerX+radius*Math.cos(startAngle);const y1=centerY+radius*Math.sin(startAngle);const x2=centerX+radius*Math.cos(endAngle);const y2=centerY+radius*Math.sin(endAngle);const largeArcFlag=anglePerSlice>Math.PI?1:0;return\"M \".concat(centerX,\" \").concat(centerY,\" L \").concat(x1,\" \").concat(y1,\" A \").concat(radius,\" \").concat(radius,\" 0 \").concat(largeArcFlag,\" 1 \").concat(x2,\" \").concat(y2,\" Z\");};return/*#__PURE__*/_jsx(\"div\",{className:\"pizza-svg-container\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"300\",height:\"300\",viewBox:\"0 0 300 300\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:centerX,cy:centerY,r:radius+8,fill:\"#d68910\",stroke:\"#b7950b\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"circle\",{cx:centerX,cy:centerY,r:radius,fill:\"#f39c12\"}),Array.from({length:totalSlices},(_,index)=>/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"path\",{d:createSlicePath(index),fill:selectedSlices[index]?\"#2ecc71\":\"#f39c12\",stroke:\"#d68910\",strokeWidth:\"2\",className:\"pizza-slice-svg\",onClick:()=>onSliceClick(index),style:{cursor:'pointer',filter:selectedSlices[index]?'brightness(1.1) drop-shadow(0 0 10px rgba(46, 204, 113, 0.5))':'none',transition:'all 0.3s ease'}}),totalSlices<=8&&/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"circle\",{cx:centerX+radius*0.4*Math.cos((index+0.3)*(2*Math.PI)/totalSlices-Math.PI/2),cy:centerY+radius*0.4*Math.sin((index+0.3)*(2*Math.PI)/totalSlices-Math.PI/2),r:\"4\",fill:\"#e74c3c\"}),/*#__PURE__*/_jsx(\"circle\",{cx:centerX+radius*0.6*Math.cos((index+0.7)*(2*Math.PI)/totalSlices-Math.PI/2),cy:centerY+radius*0.6*Math.sin((index+0.7)*(2*Math.PI)/totalSlices-Math.PI/2),r:\"3\",fill:\"#27ae60\"}),/*#__PURE__*/_jsx(\"circle\",{cx:centerX+radius*0.5*Math.cos((index+0.5)*(2*Math.PI)/totalSlices-Math.PI/2),cy:centerY+radius*0.5*Math.sin((index+0.5)*(2*Math.PI)/totalSlices-Math.PI/2),r:\"3\",fill:\"#f1c40f\"})]})]},index)),Array.from({length:totalSlices},(_,index)=>{const angle=index*(2*Math.PI)/totalSlices-Math.PI/2;const x=centerX+radius*Math.cos(angle);const y=centerY+radius*Math.sin(angle);return/*#__PURE__*/_jsx(\"line\",{x1:centerX,y1:centerY,x2:x,y2:y,stroke:\"#d68910\",strokeWidth:\"3\"},\"divider-\".concat(index));})]})});};export default PizzaSVG;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "PizzaSVG", "_ref", "totalSlices", "selectedSlices", "onSliceClick", "centerX", "centerY", "radius", "createSlicePath", "index", "anglePerSlice", "Math", "PI", "startAngle", "endAngle", "x1", "cos", "y1", "sin", "x2", "y2", "largeArcFlag", "concat", "className", "children", "width", "height", "viewBox", "cx", "cy", "r", "fill", "stroke", "strokeWidth", "Array", "from", "length", "_", "d", "onClick", "style", "cursor", "filter", "transition", "angle", "x", "y"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/PizzaSVG.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface PizzaSVGProps {\n  totalSlices: number;\n  selectedSlices: boolean[];\n  onSliceClick: (index: number) => void;\n}\n\nconst PizzaSVG: React.FC<PizzaSVGProps> = ({ totalSlices, selectedSlices, onSliceClick }) => {\n  const centerX = 150;\n  const centerY = 150;\n  const radius = 130;\n\n  const createSlicePath = (index: number) => {\n    const anglePerSlice = (2 * Math.PI) / totalSlices;\n    const startAngle = index * anglePerSlice - Math.PI / 2; // Start from top\n    const endAngle = (index + 1) * anglePerSlice - Math.PI / 2;\n\n    const x1 = centerX + radius * Math.cos(startAngle);\n    const y1 = centerY + radius * Math.sin(startAngle);\n    const x2 = centerX + radius * Math.cos(endAngle);\n    const y2 = centerY + radius * Math.sin(endAngle);\n\n    const largeArcFlag = anglePerSlice > Math.PI ? 1 : 0;\n\n    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;\n  };\n\n  return (\n    <div className=\"pizza-svg-container\">\n      <svg width=\"300\" height=\"300\" viewBox=\"0 0 300 300\">\n        {/* Pizza base */}\n        <circle\n          cx={centerX}\n          cy={centerY}\n          r={radius + 8}\n          fill=\"#d68910\"\n          stroke=\"#b7950b\"\n          strokeWidth=\"4\"\n        />\n        <circle\n          cx={centerX}\n          cy={centerY}\n          r={radius}\n          fill=\"#f39c12\"\n        />\n        \n        {/* Pizza slices */}\n        {Array.from({ length: totalSlices }, (_, index) => (\n          <g key={index}>\n            <path\n              d={createSlicePath(index)}\n              fill={selectedSlices[index] ? \"#2ecc71\" : \"#f39c12\"}\n              stroke=\"#d68910\"\n              strokeWidth=\"2\"\n              className=\"pizza-slice-svg\"\n              onClick={() => onSliceClick(index)}\n              style={{\n                cursor: 'pointer',\n                filter: selectedSlices[index] ? 'brightness(1.1) drop-shadow(0 0 10px rgba(46, 204, 113, 0.5))' : 'none',\n                transition: 'all 0.3s ease'\n              }}\n            />\n            \n            {/* Pizza toppings */}\n            {totalSlices <= 8 && (\n              <g>\n                <circle\n                  cx={centerX + (radius * 0.4) * Math.cos((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  cy={centerY + (radius * 0.4) * Math.sin((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  r=\"4\"\n                  fill=\"#e74c3c\"\n                />\n                <circle\n                  cx={centerX + (radius * 0.6) * Math.cos((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  cy={centerY + (radius * 0.6) * Math.sin((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  r=\"3\"\n                  fill=\"#27ae60\"\n                />\n                <circle\n                  cx={centerX + (radius * 0.5) * Math.cos((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  cy={centerY + (radius * 0.5) * Math.sin((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  r=\"3\"\n                  fill=\"#f1c40f\"\n                />\n              </g>\n            )}\n          </g>\n        ))}\n        \n        {/* Slice dividers */}\n        {Array.from({ length: totalSlices }, (_, index) => {\n          const angle = index * (2 * Math.PI) / totalSlices - Math.PI / 2;\n          const x = centerX + radius * Math.cos(angle);\n          const y = centerY + radius * Math.sin(angle);\n          \n          return (\n            <line\n              key={`divider-${index}`}\n              x1={centerX}\n              y1={centerY}\n              x2={x}\n              y2={y}\n              stroke=\"#d68910\"\n              strokeWidth=\"3\"\n            />\n          );\n        })}\n      </svg>\n    </div>\n  );\n};\n\nexport default PizzaSVG;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ1B,KAAM,CAAAC,QAAiC,CAAGC,IAAA,EAAmD,IAAlD,CAAEC,WAAW,CAAEC,cAAc,CAAEC,YAAa,CAAC,CAAAH,IAAA,CACtF,KAAM,CAAAI,OAAO,CAAG,GAAG,CACnB,KAAM,CAAAC,OAAO,CAAG,GAAG,CACnB,KAAM,CAAAC,MAAM,CAAG,GAAG,CAElB,KAAM,CAAAC,eAAe,CAAIC,KAAa,EAAK,CACzC,KAAM,CAAAC,aAAa,CAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAAIV,WAAW,CACjD,KAAM,CAAAW,UAAU,CAAGJ,KAAK,CAAGC,aAAa,CAAGC,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE;AACxD,KAAM,CAAAE,QAAQ,CAAG,CAACL,KAAK,CAAG,CAAC,EAAIC,aAAa,CAAGC,IAAI,CAACC,EAAE,CAAG,CAAC,CAE1D,KAAM,CAAAG,EAAE,CAAGV,OAAO,CAAGE,MAAM,CAAGI,IAAI,CAACK,GAAG,CAACH,UAAU,CAAC,CAClD,KAAM,CAAAI,EAAE,CAAGX,OAAO,CAAGC,MAAM,CAAGI,IAAI,CAACO,GAAG,CAACL,UAAU,CAAC,CAClD,KAAM,CAAAM,EAAE,CAAGd,OAAO,CAAGE,MAAM,CAAGI,IAAI,CAACK,GAAG,CAACF,QAAQ,CAAC,CAChD,KAAM,CAAAM,EAAE,CAAGd,OAAO,CAAGC,MAAM,CAAGI,IAAI,CAACO,GAAG,CAACJ,QAAQ,CAAC,CAEhD,KAAM,CAAAO,YAAY,CAAGX,aAAa,CAAGC,IAAI,CAACC,EAAE,CAAG,CAAC,CAAG,CAAC,CAEpD,WAAAU,MAAA,CAAYjB,OAAO,MAAAiB,MAAA,CAAIhB,OAAO,QAAAgB,MAAA,CAAMP,EAAE,MAAAO,MAAA,CAAIL,EAAE,QAAAK,MAAA,CAAMf,MAAM,MAAAe,MAAA,CAAIf,MAAM,QAAAe,MAAA,CAAMD,YAAY,QAAAC,MAAA,CAAMH,EAAE,MAAAG,MAAA,CAAIF,EAAE,OACpG,CAAC,CAED,mBACEvB,IAAA,QAAK0B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCzB,KAAA,QAAK0B,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC,KAAK,CAACC,OAAO,CAAC,aAAa,CAAAH,QAAA,eAEjD3B,IAAA,WACE+B,EAAE,CAAEvB,OAAQ,CACZwB,EAAE,CAAEvB,OAAQ,CACZwB,CAAC,CAAEvB,MAAM,CAAG,CAAE,CACdwB,IAAI,CAAC,SAAS,CACdC,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAC,GAAG,CAChB,CAAC,cACFpC,IAAA,WACE+B,EAAE,CAAEvB,OAAQ,CACZwB,EAAE,CAAEvB,OAAQ,CACZwB,CAAC,CAAEvB,MAAO,CACVwB,IAAI,CAAC,SAAS,CACf,CAAC,CAGDG,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAElC,WAAY,CAAC,CAAE,CAACmC,CAAC,CAAE5B,KAAK,gBAC5CV,KAAA,MAAAyB,QAAA,eACE3B,IAAA,SACEyC,CAAC,CAAE9B,eAAe,CAACC,KAAK,CAAE,CAC1BsB,IAAI,CAAE5B,cAAc,CAACM,KAAK,CAAC,CAAG,SAAS,CAAG,SAAU,CACpDuB,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAC,GAAG,CACfV,SAAS,CAAC,iBAAiB,CAC3BgB,OAAO,CAAEA,CAAA,GAAMnC,YAAY,CAACK,KAAK,CAAE,CACnC+B,KAAK,CAAE,CACLC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAEvC,cAAc,CAACM,KAAK,CAAC,CAAG,+DAA+D,CAAG,MAAM,CACxGkC,UAAU,CAAE,eACd,CAAE,CACH,CAAC,CAGDzC,WAAW,EAAI,CAAC,eACfH,KAAA,MAAAyB,QAAA,eACE3B,IAAA,WACE+B,EAAE,CAAEvB,OAAO,CAAIE,MAAM,CAAG,GAAG,CAAII,IAAI,CAACK,GAAG,CAAC,CAACP,KAAK,CAAG,GAAG,GAAK,CAAC,CAAGE,IAAI,CAACC,EAAE,CAAC,CAAGV,WAAW,CAAGS,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE,CACnGiB,EAAE,CAAEvB,OAAO,CAAIC,MAAM,CAAG,GAAG,CAAII,IAAI,CAACO,GAAG,CAAC,CAACT,KAAK,CAAG,GAAG,GAAK,CAAC,CAAGE,IAAI,CAACC,EAAE,CAAC,CAAGV,WAAW,CAAGS,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE,CACnGkB,CAAC,CAAC,GAAG,CACLC,IAAI,CAAC,SAAS,CACf,CAAC,cACFlC,IAAA,WACE+B,EAAE,CAAEvB,OAAO,CAAIE,MAAM,CAAG,GAAG,CAAII,IAAI,CAACK,GAAG,CAAC,CAACP,KAAK,CAAG,GAAG,GAAK,CAAC,CAAGE,IAAI,CAACC,EAAE,CAAC,CAAGV,WAAW,CAAGS,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE,CACnGiB,EAAE,CAAEvB,OAAO,CAAIC,MAAM,CAAG,GAAG,CAAII,IAAI,CAACO,GAAG,CAAC,CAACT,KAAK,CAAG,GAAG,GAAK,CAAC,CAAGE,IAAI,CAACC,EAAE,CAAC,CAAGV,WAAW,CAAGS,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE,CACnGkB,CAAC,CAAC,GAAG,CACLC,IAAI,CAAC,SAAS,CACf,CAAC,cACFlC,IAAA,WACE+B,EAAE,CAAEvB,OAAO,CAAIE,MAAM,CAAG,GAAG,CAAII,IAAI,CAACK,GAAG,CAAC,CAACP,KAAK,CAAG,GAAG,GAAK,CAAC,CAAGE,IAAI,CAACC,EAAE,CAAC,CAAGV,WAAW,CAAGS,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE,CACnGiB,EAAE,CAAEvB,OAAO,CAAIC,MAAM,CAAG,GAAG,CAAII,IAAI,CAACO,GAAG,CAAC,CAACT,KAAK,CAAG,GAAG,GAAK,CAAC,CAAGE,IAAI,CAACC,EAAE,CAAC,CAAGV,WAAW,CAAGS,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE,CACnGkB,CAAC,CAAC,GAAG,CACLC,IAAI,CAAC,SAAS,CACf,CAAC,EACD,CACJ,GArCKtB,KAsCL,CACJ,CAAC,CAGDyB,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAElC,WAAY,CAAC,CAAE,CAACmC,CAAC,CAAE5B,KAAK,GAAK,CACjD,KAAM,CAAAmC,KAAK,CAAGnC,KAAK,EAAI,CAAC,CAAGE,IAAI,CAACC,EAAE,CAAC,CAAGV,WAAW,CAAGS,IAAI,CAACC,EAAE,CAAG,CAAC,CAC/D,KAAM,CAAAiC,CAAC,CAAGxC,OAAO,CAAGE,MAAM,CAAGI,IAAI,CAACK,GAAG,CAAC4B,KAAK,CAAC,CAC5C,KAAM,CAAAE,CAAC,CAAGxC,OAAO,CAAGC,MAAM,CAAGI,IAAI,CAACO,GAAG,CAAC0B,KAAK,CAAC,CAE5C,mBACE/C,IAAA,SAEEkB,EAAE,CAAEV,OAAQ,CACZY,EAAE,CAAEX,OAAQ,CACZa,EAAE,CAAE0B,CAAE,CACNzB,EAAE,CAAE0B,CAAE,CACNd,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAC,GAAG,aAAAX,MAAA,CANCb,KAAK,CAOtB,CAAC,CAEN,CAAC,CAAC,EACC,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}