{"ast": null, "code": "// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects {\n  constructor() {\n    this.audioContext = null;\n    // Inicializace pouze pokud je podporováno\n    if (typeof window !== 'undefined' && 'AudioContext' in window) {\n      this.audioContext = new AudioContext();\n    }\n  }\n\n  // Zvuk pro řezání pizzy\n  playSliceSound() {\n    if (!this.audioContext) return;\n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);\n    oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);\n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);\n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.1);\n  }\n\n  // Zvuk pro výběr dílku\n  playSelectSound() {\n    if (!this.audioContext) return;\n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime);\n    oscillator.type = 'sine';\n    gainNode.gain.setValueAtTime(0.05, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.05);\n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.05);\n  }\n\n  // Zvuk pro správnou odpověď\n  playSuccessSound() {\n    if (!this.audioContext) return;\n    const frequencies = [523, 659, 784]; // C, E, G\n\n    frequencies.forEach((freq, index) => {\n      const oscillator = this.audioContext.createOscillator();\n      const gainNode = this.audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(this.audioContext.destination);\n      oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime + index * 0.1);\n      oscillator.type = 'sine';\n      gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime + index * 0.1);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + index * 0.1 + 0.2);\n      oscillator.start(this.audioContext.currentTime + index * 0.1);\n      oscillator.stop(this.audioContext.currentTime + index * 0.1 + 0.2);\n    });\n  }\n\n  // Zvuk pro špatnou odpověď\n  playErrorSound() {\n    if (!this.audioContext) return;\n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);\n    oscillator.type = 'sawtooth';\n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);\n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.3);\n  }\n}\nexport const soundEffects = new SoundEffects();", "map": {"version": 3, "names": ["SoundEffects", "constructor", "audioContext", "window", "AudioContext", "playSliceSound", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "playSelectSound", "type", "playSuccessSound", "frequencies", "for<PERSON>ach", "freq", "index", "playErrorSound", "soundEffects"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/SoundEffects.tsx"], "sourcesContent": ["// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects {\n  private audioContext: AudioContext | null = null;\n\n  constructor() {\n    // Inicializace pouze pokud je podporováno\n    if (typeof window !== 'undefined' && 'AudioContext' in window) {\n      this.audioContext = new AudioContext();\n    }\n  }\n\n  // Zvuk pro řezání pizzy\n  playSliceSound() {\n    if (!this.audioContext) return;\n    \n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    \n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    \n    oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);\n    oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);\n    \n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);\n    \n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.1);\n  }\n\n  // Zvuk pro výběr dílku\n  playSelectSound() {\n    if (!this.audioContext) return;\n    \n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    \n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    \n    oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime);\n    oscillator.type = 'sine';\n    \n    gainNode.gain.setValueAtTime(0.05, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.05);\n    \n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.05);\n  }\n\n  // Zvuk pro správnou odpověď\n  playSuccessSound() {\n    if (!this.audioContext) return;\n    \n    const frequencies = [523, 659, 784]; // C, E, G\n    \n    frequencies.forEach((freq, index) => {\n      const oscillator = this.audioContext!.createOscillator();\n      const gainNode = this.audioContext!.createGain();\n      \n      oscillator.connect(gainNode);\n      gainNode.connect(this.audioContext!.destination);\n      \n      oscillator.frequency.setValueAtTime(freq, this.audioContext!.currentTime + index * 0.1);\n      oscillator.type = 'sine';\n      \n      gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime + index * 0.1);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + index * 0.1 + 0.2);\n      \n      oscillator.start(this.audioContext!.currentTime + index * 0.1);\n      oscillator.stop(this.audioContext!.currentTime + index * 0.1 + 0.2);\n    });\n  }\n\n  // Zvuk pro špatnou odpověď\n  playErrorSound() {\n    if (!this.audioContext) return;\n    \n    const oscillator = this.audioContext.createOscillator();\n    const gainNode = this.audioContext.createGain();\n    \n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext.destination);\n    \n    oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);\n    oscillator.type = 'sawtooth';\n    \n    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);\n    \n    oscillator.start(this.audioContext.currentTime);\n    oscillator.stop(this.audioContext.currentTime + 0.3);\n  }\n}\n\nexport const soundEffects = new SoundEffects();\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,YAAY,CAAC;EAGxBC,WAAWA,CAAA,EAAG;IAAA,KAFNC,YAAY,GAAwB,IAAI;IAG9C;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,cAAc,IAAIA,MAAM,EAAE;MAC7D,IAAI,CAACD,YAAY,GAAG,IAAIE,YAAY,CAAC,CAAC;IACxC;EACF;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;IAExB,MAAMI,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAACO,UAAU,CAAC,CAAC;IAE/CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;IAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAACS,WAAW,CAAC;IAE/CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC;IACvER,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,EAAE,IAAI,CAACb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;IAE3FN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC;IAChEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;IAErFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAACY,WAAW,CAAC;IAC/CR,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;EACtD;;EAEA;EACAK,eAAeA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACjB,YAAY,EAAE;IAExB,MAAMI,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAACO,UAAU,CAAC,CAAC;IAE/CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;IAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAACS,WAAW,CAAC;IAE/CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC;IACvER,UAAU,CAACc,IAAI,GAAG,MAAM;IAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,IAAI,EAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC;IACjEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACb,YAAY,CAACY,WAAW,GAAG,IAAI,CAAC;IAEtFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAACY,WAAW,CAAC;IAC/CR,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,IAAI,CAAC;EACvD;;EAEA;EACAO,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACnB,YAAY,EAAE;IAExB,MAAMoB,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;IAErCA,WAAW,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACnC,MAAMnB,UAAU,GAAG,IAAI,CAACJ,YAAY,CAAEK,gBAAgB,CAAC,CAAC;MACxD,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAAEO,UAAU,CAAC,CAAC;MAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAAES,WAAW,CAAC;MAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAACW,IAAI,EAAE,IAAI,CAACtB,YAAY,CAAEY,WAAW,GAAGW,KAAK,GAAG,GAAG,CAAC;MACvFnB,UAAU,CAACc,IAAI,GAAG,MAAM;MAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAE,IAAI,CAACX,YAAY,CAAEY,WAAW,GAAGW,KAAK,GAAG,GAAG,CAAC;MAC/EjB,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACb,YAAY,CAAEY,WAAW,GAAGW,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;MAEpGnB,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAAEY,WAAW,GAAGW,KAAK,GAAG,GAAG,CAAC;MAC9DnB,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAAEY,WAAW,GAAGW,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;IACrE,CAAC,CAAC;EACJ;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACxB,YAAY,EAAE;IAExB,MAAMI,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACK,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAACO,UAAU,CAAC,CAAC;IAE/CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;IAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACR,YAAY,CAACS,WAAW,CAAC;IAE/CL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,EAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC;IACvER,UAAU,CAACc,IAAI,GAAG,UAAU;IAE5BZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,EAAE,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC;IAChEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAACb,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;IAErFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACf,YAAY,CAACY,WAAW,CAAC;IAC/CR,UAAU,CAACY,IAAI,CAAC,IAAI,CAAChB,YAAY,CAACY,WAAW,GAAG,GAAG,CAAC;EACtD;AACF;AAEA,OAAO,MAAMa,YAAY,GAAG,IAAI3B,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}