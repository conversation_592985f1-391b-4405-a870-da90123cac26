{"ast": null, "code": "var _jsxFileName = \"C:\\\\Work\\\\new_AI\\\\PizzaFraction\\\\pizza-fractions\\\\src\\\\components\\\\PizzaChef.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PizzaChef = ({\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `pizza-chef ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"img\", {\n      src: \"/pizzabaker.png\",\n      alt: \"Pizza peka\\u0159\",\n      width: \"120\",\n      height: \"120\",\n      style: {\n        borderRadius: '10px',\n        objectFit: 'cover'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = PizzaChef;\nexport default PizzaChef;\nvar _c;\n$RefreshReg$(_c, \"PizzaChef\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PizzaChef", "className", "children", "src", "alt", "width", "height", "style", "borderRadius", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/PizzaChef.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface PizzaChefProps {\n  className?: string;\n}\n\nconst PizzaChef: React.FC<PizzaChefProps> = ({ className = '' }) => {\n  return (\n    <div className={`pizza-chef ${className}`}>\n      <img\n        src=\"/pizzabaker.png\"\n        alt=\"Pizza pekař\"\n        width=\"120\"\n        height=\"120\"\n        style={{\n          borderRadius: '10px',\n          objectFit: 'cover'\n        }}\n      />\n    </div>\n  );\n};\n\nexport default PizzaChef;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM1B,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAClE,oBACEF,OAAA;IAAKE,SAAS,EAAE,cAAcA,SAAS,EAAG;IAAAC,QAAA,eACxCH,OAAA;MACEI,GAAG,EAAC,iBAAiB;MACrBC,GAAG,EAAC,kBAAa;MACjBC,KAAK,EAAC,KAAK;MACXC,MAAM,EAAC,KAAK;MACZC,KAAK,EAAE;QACLC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACC,EAAA,GAfId,SAAmC;AAiBzC,eAAeA,SAAS;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}