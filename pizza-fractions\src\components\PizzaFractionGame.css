.pizza-game {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  color: #2c3e50;
  padding: 20px;
  box-sizing: border-box;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.game-header {
  text-align: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.game-header h1 {
  font-size: 2.5rem;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  color: #e74c3c;
}

.game-info {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 15px;
}

.game-info > div {
  background: #3498db;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 1.1rem;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.score {
  background: #27ae60 !important;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3) !important;
}

.target-fraction {
  background: #e74c3c !important;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;
}

.game-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.hint-button,
.reset-button {
  background: linear-gradient(45deg, #9b59b6, #8e44ad);
  color: white;
  border: none;
  padding: 8px 16px;
  font-size: 0.9rem;
  border-radius: 20px;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
  transition: all 0.3s ease;
  font-family: inherit;
  font-weight: bold;
}

.reset-button {
  background: linear-gradient(45deg, #e67e22, #d35400);
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
}

.hint-button:hover,
.reset-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

.reset-button:hover {
  box-shadow: 0 6px 20px rgba(230, 126, 34, 0.4);
}

.instructions {
  text-align: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  padding: 15px;
  font-size: 1.3rem;
  font-weight: bold;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feedback {
  margin-top: 15px;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: bold;
  animation: fadeIn 0.5s ease;
}

.feedback.success {
  background: linear-gradient(45deg, #2ecc71, #27ae60);
  color: white;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.feedback.error {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.hint {
  margin-top: 15px;
  padding: 15px 20px;
  border-radius: 15px;
  font-size: 1.1rem;
  background: linear-gradient(45deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
  animation: fadeIn 0.5s ease;
}

.game-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.knife-section {
  display: flex;
  justify-content: center;
}

.knife-button {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(238, 90, 82, 0.4);
  transition: all 0.3s ease;
  font-family: inherit;
  font-weight: bold;
}

.knife-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(238, 90, 82, 0.6);
}

.knife-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pizza-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.pizza-svg-container {
  display: flex;
  justify-content: center;
  align-items: center;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

.pizza-svg-container svg {
  max-width: 100%;
  height: auto;
}

.pizza-slice-svg {
  transition: all 0.3s ease;
}

.pizza-slice-svg:hover {
  filter: brightness(1.1);
  transform-origin: center;
}



.controls {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.submit-button,
.next-round-button {
  background: linear-gradient(45deg, #2ecc71, #27ae60);
  color: white;
  border: none;
  padding: 15px 30px;
  font-size: 1.3rem;
  border-radius: 50px;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
  transition: all 0.3s ease;
  font-family: inherit;
  font-weight: bold;
}

.submit-button:hover,
.next-round-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(46, 204, 113, 0.6);
}

/* Responzivní design pro mobilní zařízení */
@media (max-width: 768px) {
  .pizza-game {
    padding: 10px;
  }
  
  .game-header h1 {
    font-size: 2rem;
  }
  
  .game-info {
    flex-direction: column;
    align-items: center;
  }
  
  .game-info > div {
    font-size: 1rem;
    padding: 8px 16px;
  }
  
  .instructions {
    font-size: 1.1rem;
    padding: 12px;
  }
  
  .pizza-svg-container svg {
    width: 250px;
    height: 250px;
  }
  
  .knife-button,
  .submit-button,
  .next-round-button {
    font-size: 1.2rem;
    padding: 12px 24px;
  }

  .hint-button,
  .reset-button {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  .game-controls {
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .game-header h1 {
    font-size: 1.8rem;
  }

  .pizza-svg-container svg {
    width: 200px;
    height: 200px;
  }

  .knife-button,
  .submit-button,
  .next-round-button {
    font-size: 1rem;
    padding: 10px 20px;
  }

  .instructions {
    font-size: 1rem;
  }
}

/* Animace pro úspěšné dokončení */
@keyframes celebrate {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}
