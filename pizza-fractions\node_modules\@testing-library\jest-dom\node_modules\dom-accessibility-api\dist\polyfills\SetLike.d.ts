declare global {
    class Set<T> {
        constructor(items?: T[]);
        add(value: T): this;
        clear(): void;
        delete(value: T): boolean;
        forEach(callbackfn: (value: T, value2: T, set: Set<T>) => void, thisArg?: unknown): void;
        has(value: T): boolean;
        readonly size: number;
    }
}
declare const _default: typeof Set;
export default _default;
//# sourceMappingURL=SetLike.d.ts.map