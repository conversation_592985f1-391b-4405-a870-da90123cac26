// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API
export class SoundEffects {
  private audioContext: AudioContext | null = null;

  constructor() {
    // Inicializace pouze pokud je podporováno
    if (typeof window !== 'undefined' && 'AudioContext' in window) {
      this.audioContext = new AudioContext();
    }
  }

  // Aktivuje AudioContext při první interakci
  private async ensureAudioContext() {
    if (!this.audioContext) return false;

    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume();
      } catch (error) {
        console.warn('Nepodařilo se aktivovat AudioContext:', error);
        return false;
      }
    }
    return true;
  }

  // Zvuk pro řezání pizzy
  async playSliceSound() {
    if (!(await this.ensureAudioContext())) return;

    const oscillator = this.audioContext!.createOscillator();
    const gainNode = this.audioContext!.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext!.destination);

    oscillator.frequency.setValueAtTime(800, this.audioContext!.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext!.currentTime + 0.1);

    gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.1);

    oscillator.start(this.audioContext!.currentTime);
    oscillator.stop(this.audioContext!.currentTime + 0.1);
  }

  // Zvuk pro výběr dílku
  async playSelectSound() {
    if (!(await this.ensureAudioContext())) return;

    const oscillator = this.audioContext!.createOscillator();
    const gainNode = this.audioContext!.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext!.destination);

    oscillator.frequency.setValueAtTime(600, this.audioContext!.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.05, this.audioContext!.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.05);

    oscillator.start(this.audioContext!.currentTime);
    oscillator.stop(this.audioContext!.currentTime + 0.05);
  }

  // Zvuk pro správnou odpověď
  async playSuccessSound() {
    if (!(await this.ensureAudioContext())) return;

    const frequencies = [523, 659, 784]; // C, E, G

    frequencies.forEach((freq, index) => {
      const oscillator = this.audioContext!.createOscillator();
      const gainNode = this.audioContext!.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext!.destination);

      oscillator.frequency.setValueAtTime(freq, this.audioContext!.currentTime + index * 0.1);
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime + index * 0.1);
      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + index * 0.1 + 0.2);

      oscillator.start(this.audioContext!.currentTime + index * 0.1);
      oscillator.stop(this.audioContext!.currentTime + index * 0.1 + 0.2);
    });
  }

  // Zvuk pro špatnou odpověď
  async playErrorSound() {
    if (!(await this.ensureAudioContext())) return;

    const oscillator = this.audioContext!.createOscillator();
    const gainNode = this.audioContext!.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext!.destination);

    oscillator.frequency.setValueAtTime(200, this.audioContext!.currentTime);
    oscillator.type = 'sawtooth';

    gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.3);

    oscillator.start(this.audioContext!.currentTime);
    oscillator.stop(this.audioContext!.currentTime + 0.3);
  }
}

export const soundEffects = new SoundEffects();
