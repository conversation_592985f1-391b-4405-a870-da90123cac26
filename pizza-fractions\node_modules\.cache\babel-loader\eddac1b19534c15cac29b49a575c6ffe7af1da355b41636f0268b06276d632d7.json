{"ast": null, "code": "import React from'react';import'./App.css';import PizzaFractionGame from'./components/PizzaFractionGame';import{jsx as _jsx}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(PizzaFractionGame,{})});}export default App;", "map": {"version": 3, "names": ["React", "PizzaFractionGame", "jsx", "_jsx", "App", "className", "children"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport './App.css';\nimport PizzaFractionGame from './components/PizzaFractionGame';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <PizzaFractionGame />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,WAAW,CAClB,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE/D,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACED,IAAA,QAAKE,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBH,IAAA,CAACF,iBAAiB,GAAE,CAAC,CAClB,CAAC,CAEV,CAEA,cAAe,CAAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}