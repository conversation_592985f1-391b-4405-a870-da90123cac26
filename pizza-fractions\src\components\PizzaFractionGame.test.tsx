import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import PizzaFractionGame from './PizzaFractionGame';

describe('PizzaFractionGame', () => {
  test('renders game title', () => {
    render(<PizzaFractionGame />);
    const titleElement = screen.getByText(/Pizza Zlomky/i);
    expect(titleElement).toBeInTheDocument();
  });

  test('renders initial score as 0', () => {
    render(<PizzaFractionGame />);
    const scoreElement = screen.getByText(/Skóre: 0/i);
    expect(scoreElement).toBeInTheDocument();
  });

  test('renders knife button initially', () => {
    render(<PizzaFractionGame />);
    const knifeButton = screen.getByText(/Řezat pizzu/i);
    expect(knifeButton).toBeInTheDocument();
  });

  test('shows cutting instructions initially', () => {
    render(<PizzaFractionGame />);
    const instructions = screen.getByText(/Klikni na nůž pro řezání pizzy/i);
    expect(instructions).toBeInTheDocument();
  });

  test('knife button increases slice count', () => {
    render(<PizzaFractionGame />);
    const knifeButton = screen.getByText(/Řezat pizzu/i);
    
    // Initial state should show 0/1
    expect(screen.getByText(/Tvůj zlomek: 0\/1/i)).toBeInTheDocument();
    
    // Click knife button
    fireEvent.click(knifeButton);
    
    // Should now show 0/2
    expect(screen.getByText(/Tvůj zlomek: 0\/2/i)).toBeInTheDocument();
  });

  test('displays target fraction', () => {
    render(<PizzaFractionGame />);
    // Should display some target fraction (format: Cíl: X/Y)
    const targetElement = screen.getByText(/Cíl: \d+\/\d+/);
    expect(targetElement).toBeInTheDocument();
  });
});
