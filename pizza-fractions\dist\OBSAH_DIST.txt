🍕 PIZZA FRACTIONS - OBSAH SLOŽKY /dist
=============================================

📊 CELKOVÁ VELIKOST: ~1.9 MB (nekomprimováno)
📊 GZIP VELIKOST: ~65 kB (optimalizováno pro web)

📁 STRUKTURA SOUBORŮ:
=====================

📄 HLAVNÍ SOUBORY:
------------------
✅ index.html              - Hlavní HTML stránka (vstupní bod)
✅ pizzabaker.png          - Obr<PERSON><PERSON><PERSON> pekař<PERSON> (záhlaví hry)
✅ favicon.ico             - Ikona webu
✅ manifest.json           - PWA manifest pro mobilní zařízení
✅ robots.txt              - SEO soubor pro vyhledávače
✅ .htaccess               - Apache konfigurace (volitelné)

📄 DOKUMENTACE:
---------------
✅ README_DEPLOYMENT.md    - Návod na nasazení
✅ OBSAH_DIST.txt         - Tento soubor

📁 SLOŽKA static/:
==================

📁 static/css/:
--------------
✅ main.58a8b5fe.css      - Optimalizované CSS styly
✅ main.58a8b5fe.css.map  - Source map pro debugging

📁 static/js/:
-------------
✅ main.366240f3.js           - Hlavní JavaScript aplikace
✅ main.366240f3.js.map       - Source map pro debugging
✅ main.366240f3.js.LICENSE.txt - Licence použitých knihoven
✅ 453.f52caa2a.chunk.js      - Dodatečný JavaScript chunk
✅ 453.f52caa2a.chunk.js.map  - Source map pro chunk

📄 METADATA:
============
✅ asset-manifest.json     - Manifest všech assets

🚀 NASAZENÍ NA WEBHOSTING:
==========================

1. Nahrajte VŠECHNY soubory a složky do kořenového adresáře webhostingu
2. Zachovejte strukturu složek (zejména static/)
3. Hlavní soubor: index.html
4. Aplikace je připravena k použití!

⚡ OPTIMALIZACE:
===============
- Minifikovaný JavaScript a CSS
- GZIP komprese podporována
- Optimalizované obrázky
- Rychlé načítání (< 1 sekunda na rychlém připojení)

🎯 KOMPATIBILITA:
================
- Všechny moderní prohlížeče
- Mobilní zařízení
- Tablety
- Desktop počítače
- Interaktivní projektory

📱 RESPONSIVNÍ DESIGN:
=====================
- Automatické přizpůsobení velikosti obrazovky
- Touch-friendly ovládání
- Optimalizováno pro děti

🔊 ZVUKOVÉ EFEKTY:
=================
- Generovány pomocí Web Audio API
- Žádné externí audio soubory
- Automatická aktivace při interakci

✨ FUNKCE HRY:
=============
- Interaktivní krájení pizzy
- Výběr zlomkových částí
- Okamžitá zpětná vazba
- Bodování a postupné obtížnosti
- Nápověda pro děti
- Zvukové efekty

🎓 VZDĚLÁVACÍ HODNOTA:
=====================
- Učení zlomků hravou formou
- Vizuální reprezentace matematických konceptů
- Vhodné pro děti věku ~10 let
- Použitelné ve školách na interaktivních projektorech

---
Aplikace je připravena k nasazení!
Stačí nahrát obsah této složky na webhosting.
