{"ast": null, "code": "// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects{constructor(){this.audioContext=null;// Inicializace pouze pokud je podporováno\nif(typeof window!=='undefined'&&'AudioContext'in window){this.audioContext=new AudioContext();}}// Aktivuje AudioContext při první interakci\nasync ensureAudioContext(){if(!this.audioContext)return false;if(this.audioContext.state==='suspended'){try{await this.audioContext.resume();}catch(error){console.warn('Nepodařilo se aktivovat AudioContext:',error);return false;}}return true;}// Zvuk pro řezání pizzy\nasync playSliceSound(){if(!(await this.ensureAudioContext()))return;const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(800,this.audioContext.currentTime);oscillator.frequency.exponentialRampToValueAtTime(400,this.audioContext.currentTime+0.1);gainNode.gain.setValueAtTime(0.1,this.audioContext.currentTime);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+0.1);oscillator.start(this.audioContext.currentTime);oscillator.stop(this.audioContext.currentTime+0.1);}// Zvuk pro výběr dílku\nasync playSelectSound(){if(!(await this.ensureAudioContext()))return;const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(600,this.audioContext.currentTime);oscillator.type='sine';gainNode.gain.setValueAtTime(0.05,this.audioContext.currentTime);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+0.05);oscillator.start(this.audioContext.currentTime);oscillator.stop(this.audioContext.currentTime+0.05);}// Zvuk pro správnou odpověď\nasync playSuccessSound(){if(!(await this.ensureAudioContext()))return;const frequencies=[523,659,784];// C, E, G\nfrequencies.forEach((freq,index)=>{const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(freq,this.audioContext.currentTime+index*0.1);oscillator.type='sine';gainNode.gain.setValueAtTime(0.1,this.audioContext.currentTime+index*0.1);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+index*0.1+0.2);oscillator.start(this.audioContext.currentTime+index*0.1);oscillator.stop(this.audioContext.currentTime+index*0.1+0.2);});}// Zvuk pro špatnou odpověď\nasync playErrorSound(){if(!(await this.ensureAudioContext()))return;const oscillator=this.audioContext.createOscillator();const gainNode=this.audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(this.audioContext.destination);oscillator.frequency.setValueAtTime(200,this.audioContext.currentTime);oscillator.type='sawtooth';gainNode.gain.setValueAtTime(0.1,this.audioContext.currentTime);gainNode.gain.exponentialRampToValueAtTime(0.01,this.audioContext.currentTime+0.3);oscillator.start(this.audioContext.currentTime);oscillator.stop(this.audioContext.currentTime+0.3);}}export const soundEffects=new SoundEffects();", "map": {"version": 3, "names": ["SoundEffects", "constructor", "audioContext", "window", "AudioContext", "ensureAudioContext", "state", "resume", "error", "console", "warn", "playSliceSound", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "playSelectSound", "type", "playSuccessSound", "frequencies", "for<PERSON>ach", "freq", "index", "playErrorSound", "soundEffects"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/SoundEffects.tsx"], "sourcesContent": ["// Jednoduchá komponenta pro zvukové efekty pomocí Web Audio API\nexport class SoundEffects {\n  private audioContext: AudioContext | null = null;\n\n  constructor() {\n    // Inicializace pouze pokud je podporováno\n    if (typeof window !== 'undefined' && 'AudioContext' in window) {\n      this.audioContext = new AudioContext();\n    }\n  }\n\n  // Aktivuje AudioContext při první interakci\n  private async ensureAudioContext() {\n    if (!this.audioContext) return false;\n\n    if (this.audioContext.state === 'suspended') {\n      try {\n        await this.audioContext.resume();\n      } catch (error) {\n        console.warn('Nepodařilo se aktivovat AudioContext:', error);\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // Zvuk pro řezání pizzy\n  async playSliceSound() {\n    if (!(await this.ensureAudioContext())) return;\n\n    const oscillator = this.audioContext!.createOscillator();\n    const gainNode = this.audioContext!.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext!.destination);\n\n    oscillator.frequency.setValueAtTime(800, this.audioContext!.currentTime);\n    oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext!.currentTime + 0.1);\n\n    gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.1);\n\n    oscillator.start(this.audioContext!.currentTime);\n    oscillator.stop(this.audioContext!.currentTime + 0.1);\n  }\n\n  // Zvuk pro výběr dílku\n  async playSelectSound() {\n    if (!(await this.ensureAudioContext())) return;\n\n    const oscillator = this.audioContext!.createOscillator();\n    const gainNode = this.audioContext!.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext!.destination);\n\n    oscillator.frequency.setValueAtTime(600, this.audioContext!.currentTime);\n    oscillator.type = 'sine';\n\n    gainNode.gain.setValueAtTime(0.05, this.audioContext!.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.05);\n\n    oscillator.start(this.audioContext!.currentTime);\n    oscillator.stop(this.audioContext!.currentTime + 0.05);\n  }\n\n  // Zvuk pro správnou odpověď\n  async playSuccessSound() {\n    if (!(await this.ensureAudioContext())) return;\n\n    const frequencies = [523, 659, 784]; // C, E, G\n\n    frequencies.forEach((freq, index) => {\n      const oscillator = this.audioContext!.createOscillator();\n      const gainNode = this.audioContext!.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(this.audioContext!.destination);\n\n      oscillator.frequency.setValueAtTime(freq, this.audioContext!.currentTime + index * 0.1);\n      oscillator.type = 'sine';\n\n      gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime + index * 0.1);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + index * 0.1 + 0.2);\n\n      oscillator.start(this.audioContext!.currentTime + index * 0.1);\n      oscillator.stop(this.audioContext!.currentTime + index * 0.1 + 0.2);\n    });\n  }\n\n  // Zvuk pro špatnou odpověď\n  async playErrorSound() {\n    if (!(await this.ensureAudioContext())) return;\n\n    const oscillator = this.audioContext!.createOscillator();\n    const gainNode = this.audioContext!.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(this.audioContext!.destination);\n\n    oscillator.frequency.setValueAtTime(200, this.audioContext!.currentTime);\n    oscillator.type = 'sawtooth';\n\n    gainNode.gain.setValueAtTime(0.1, this.audioContext!.currentTime);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext!.currentTime + 0.3);\n\n    oscillator.start(this.audioContext!.currentTime);\n    oscillator.stop(this.audioContext!.currentTime + 0.3);\n  }\n}\n\nexport const soundEffects = new SoundEffects();\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,YAAa,CAGxBC,WAAWA,CAAA,CAAG,MAFNC,YAAY,CAAwB,IAAI,CAG9C;AACA,GAAI,MAAO,CAAAC,MAAM,GAAK,WAAW,EAAI,cAAc,EAAI,CAAAA,MAAM,CAAE,CAC7D,IAAI,CAACD,YAAY,CAAG,GAAI,CAAAE,YAAY,CAAC,CAAC,CACxC,CACF,CAEA;AACA,KAAc,CAAAC,kBAAkBA,CAAA,CAAG,CACjC,GAAI,CAAC,IAAI,CAACH,YAAY,CAAE,MAAO,MAAK,CAEpC,GAAI,IAAI,CAACA,YAAY,CAACI,KAAK,GAAK,WAAW,CAAE,CAC3C,GAAI,CACF,KAAM,KAAI,CAACJ,YAAY,CAACK,MAAM,CAAC,CAAC,CAClC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAEF,KAAK,CAAC,CAC5D,MAAO,MAAK,CACd,CACF,CACA,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAG,cAAcA,CAAA,CAAG,CACrB,GAAI,EAAE,KAAM,KAAI,CAACN,kBAAkB,CAAC,CAAC,CAAC,CAAE,OAExC,KAAM,CAAAO,UAAU,CAAG,IAAI,CAACV,YAAY,CAAEW,gBAAgB,CAAC,CAAC,CACxD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACZ,YAAY,CAAEa,UAAU,CAAC,CAAC,CAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAAEe,WAAW,CAAC,CAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,CAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC,CACxER,UAAU,CAACM,SAAS,CAACG,4BAA4B,CAAC,GAAG,CAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,CAAG,GAAG,CAAC,CAE5FN,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,CAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC,CACjEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,CAAG,GAAG,CAAC,CAEtFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAAEkB,WAAW,CAAC,CAChDR,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAEkB,WAAW,CAAG,GAAG,CAAC,CACvD,CAEA;AACA,KAAM,CAAAK,eAAeA,CAAA,CAAG,CACtB,GAAI,EAAE,KAAM,KAAI,CAACpB,kBAAkB,CAAC,CAAC,CAAC,CAAE,OAExC,KAAM,CAAAO,UAAU,CAAG,IAAI,CAACV,YAAY,CAAEW,gBAAgB,CAAC,CAAC,CACxD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACZ,YAAY,CAAEa,UAAU,CAAC,CAAC,CAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAAEe,WAAW,CAAC,CAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,CAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC,CACxER,UAAU,CAACc,IAAI,CAAG,MAAM,CAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,IAAI,CAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC,CAClEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,CAAG,IAAI,CAAC,CAEvFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAAEkB,WAAW,CAAC,CAChDR,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAEkB,WAAW,CAAG,IAAI,CAAC,CACxD,CAEA;AACA,KAAM,CAAAO,gBAAgBA,CAAA,CAAG,CACvB,GAAI,EAAE,KAAM,KAAI,CAACtB,kBAAkB,CAAC,CAAC,CAAC,CAAE,OAExC,KAAM,CAAAuB,WAAW,CAAG,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAAE;AAErCA,WAAW,CAACC,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACnC,KAAM,CAAAnB,UAAU,CAAG,IAAI,CAACV,YAAY,CAAEW,gBAAgB,CAAC,CAAC,CACxD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACZ,YAAY,CAAEa,UAAU,CAAC,CAAC,CAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAAEe,WAAW,CAAC,CAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAACW,IAAI,CAAE,IAAI,CAAC5B,YAAY,CAAEkB,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAC,CACvFnB,UAAU,CAACc,IAAI,CAAG,MAAM,CAExBZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,CAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAC,CAC/EjB,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAG,GAAG,CAAC,CAEpGnB,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAAEkB,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAC,CAC9DnB,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAEkB,WAAW,CAAGW,KAAK,CAAG,GAAG,CAAG,GAAG,CAAC,CACrE,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAC,cAAcA,CAAA,CAAG,CACrB,GAAI,EAAE,KAAM,KAAI,CAAC3B,kBAAkB,CAAC,CAAC,CAAC,CAAE,OAExC,KAAM,CAAAO,UAAU,CAAG,IAAI,CAACV,YAAY,CAAEW,gBAAgB,CAAC,CAAC,CACxD,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACZ,YAAY,CAAEa,UAAU,CAAC,CAAC,CAEhDH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,YAAY,CAAEe,WAAW,CAAC,CAEhDL,UAAU,CAACM,SAAS,CAACC,cAAc,CAAC,GAAG,CAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC,CACxER,UAAU,CAACc,IAAI,CAAG,UAAU,CAE5BZ,QAAQ,CAACQ,IAAI,CAACH,cAAc,CAAC,GAAG,CAAE,IAAI,CAACjB,YAAY,CAAEkB,WAAW,CAAC,CACjEN,QAAQ,CAACQ,IAAI,CAACD,4BAA4B,CAAC,IAAI,CAAE,IAAI,CAACnB,YAAY,CAAEkB,WAAW,CAAG,GAAG,CAAC,CAEtFR,UAAU,CAACW,KAAK,CAAC,IAAI,CAACrB,YAAY,CAAEkB,WAAW,CAAC,CAChDR,UAAU,CAACY,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAEkB,WAAW,CAAG,GAAG,CAAC,CACvD,CACF,CAEA,MAAO,MAAM,CAAAa,YAAY,CAAG,GAAI,CAAAjC,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}