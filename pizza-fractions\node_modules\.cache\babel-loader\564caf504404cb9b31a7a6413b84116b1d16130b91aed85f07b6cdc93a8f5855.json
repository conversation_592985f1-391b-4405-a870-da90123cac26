{"ast": null, "code": "import React from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const PizzaChef=_ref=>{let{className=''}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:\"pizza-chef \".concat(className),children:/*#__PURE__*/_jsx(\"img\",{src:\"./pizzabaker.png\",alt:\"Pizza peka\\u0159\"})});};export default PizzaChef;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "PizzaChef", "_ref", "className", "concat", "children", "src", "alt"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/PizzaChef.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface PizzaChefProps {\n  className?: string;\n}\n\nconst PizzaChef: React.FC<PizzaChefProps> = ({ className = '' }) => {\n  return (\n    <div className={`pizza-chef ${className}`}>\n      <img\n        src=\"./pizzabaker.png\"\n        alt=\"Pizza pekař\"\n      />\n    </div>\n  );\n};\n\nexport default PizzaChef;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAM1B,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,SAAS,CAAG,EAAG,CAAC,CAAAD,IAAA,CAC7D,mBACEF,IAAA,QAAKG,SAAS,eAAAC,MAAA,CAAgBD,SAAS,CAAG,CAAAE,QAAA,cACxCL,IAAA,QACEM,GAAG,CAAC,kBAAkB,CACtBC,GAAG,CAAC,kBAAa,CAClB,CAAC,CACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}