{"ast": null, "code": "import _objectSpread from\"C:/Work/new_AI/PizzaFraction/pizza-fractions/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./PizzaFractionGame.css';import PizzaSVG from'./PizzaSVG';import{soundEffects}from'./SoundEffects';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PizzaFractionGame=()=>{const[gameState,setGameState]=useState({targetFraction:{numerator:1,denominator:2},currentFraction:{numerator:0,denominator:1},score:0,gamePhase:'cutting',selectedSlices:[],totalSlices:1,feedback:'',showHint:false});// Generuje náhodný zlomek menší než 1, vhodný pro děti\nconst generateRandomFraction=()=>{// Začneme s jednodu<PERSON><PERSON><PERSON><PERSON> pro mlad<PERSON><PERSON> děti\nconst easyFractions=[{numerator:1,denominator:2},{numerator:1,denominator:3},{numerator:2,denominator:3},{numerator:1,denominator:4},{numerator:3,denominator:4},{numerator:1,denominator:5},{numerator:2,denominator:5},{numerator:3,denominator:5},{numerator:4,denominator:5},{numerator:1,denominator:6},{numerator:5,denominator:6},{numerator:1,denominator:8},{numerator:3,denominator:8},{numerator:5,denominator:8},{numerator:7,denominator:8}];return easyFractions[Math.floor(Math.random()*easyFractions.length)];};// Spustí novou hru\nconst startNewRound=()=>{const newFraction=generateRandomFraction();setGameState({targetFraction:newFraction,currentFraction:{numerator:0,denominator:1},score:gameState.score,gamePhase:'cutting',selectedSlices:[],totalSlices:1,feedback:'',showHint:false});};// Resetuje celou hru\nconst resetGame=()=>{const newFraction=generateRandomFraction();setGameState({targetFraction:newFraction,currentFraction:{numerator:0,denominator:1},score:0,gamePhase:'cutting',selectedSlices:[],totalSlices:1,feedback:'',showHint:false});};// Zobrazí/skryje nápovědu\nconst toggleHint=()=>{setGameState(prev=>_objectSpread(_objectSpread({},prev),{},{showHint:!prev.showHint}));};// Řezání pizzy (zvyšuje počet dílků)\nconst cutPizza=()=>{if(gameState.gamePhase==='cutting'){soundEffects.playSliceSound();const newTotalSlices=gameState.totalSlices+1;setGameState(prev=>_objectSpread(_objectSpread({},prev),{},{totalSlices:newTotalSlices,currentFraction:_objectSpread(_objectSpread({},prev.currentFraction),{},{denominator:newTotalSlices}),selectedSlices:new Array(newTotalSlices).fill(false)}));}};// Výběr/zrušení výběru dílku pizzy\nconst toggleSlice=index=>{if(gameState.gamePhase==='cutting'){setGameState(prev=>_objectSpread(_objectSpread({},prev),{},{gamePhase:'selecting'}));}if(gameState.gamePhase==='selecting'){soundEffects.playSelectSound();const newSelectedSlices=[...gameState.selectedSlices];newSelectedSlices[index]=!newSelectedSlices[index];const selectedCount=newSelectedSlices.filter(Boolean).length;setGameState(prev=>_objectSpread(_objectSpread({},prev),{},{selectedSlices:newSelectedSlices,currentFraction:{numerator:selectedCount,denominator:prev.totalSlices}}));}};// Kontrola správnosti odpovědi\nconst checkAnswer=()=>{const{targetFraction,currentFraction}=gameState;// Zjednodušení zlomků pro porovnání\nconst gcd=(a,b)=>b===0?a:gcd(b,a%b);const simplifyFraction=frac=>{const divisor=gcd(frac.numerator,frac.denominator);return{numerator:frac.numerator/divisor,denominator:frac.denominator/divisor};};const simplifiedTarget=simplifyFraction(targetFraction);const simplifiedCurrent=simplifyFraction(currentFraction);return simplifiedTarget.numerator===simplifiedCurrent.numerator&&simplifiedTarget.denominator===simplifiedCurrent.denominator;};// Potvrzení odpovědi\nconst submitAnswer=()=>{if(gameState.gamePhase==='selecting'){const isCorrect=checkAnswer();if(isCorrect){soundEffects.playSuccessSound();setGameState(prev=>_objectSpread(_objectSpread({},prev),{},{score:prev.score+1,gamePhase:'complete',feedback:'🎉 Výborně! Správná odpověď!'}));}else{soundEffects.playErrorSound();// Špatná odpověď - resetuj výběr\nsetGameState(prev=>_objectSpread(_objectSpread({},prev),{},{selectedSlices:new Array(prev.totalSlices).fill(false),currentFraction:{numerator:0,denominator:prev.totalSlices},feedback:'❌ Zkus to znovu! Zkontroluj si zlomek.'}));}}};// Inicializace první hry\nuseEffect(()=>{const newFraction=generateRandomFraction();setGameState({targetFraction:newFraction,currentFraction:{numerator:0,denominator:1},score:0,gamePhase:'cutting',selectedSlices:[],totalSlices:1,feedback:'',showHint:false});},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"pizza-game\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"game-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\uD83C\\uDF55 Pizza Zlomky\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"game-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"score\",children:[\"Sk\\xF3re: \",gameState.score]}),/*#__PURE__*/_jsxs(\"div\",{className:\"target-fraction\",children:[\"C\\xEDl: \",gameState.targetFraction.numerator,\"/\",gameState.targetFraction.denominator]}),/*#__PURE__*/_jsxs(\"div\",{className:\"current-fraction\",children:[\"Tv\\u016Fj zlomek: \",gameState.currentFraction.numerator,\"/\",gameState.currentFraction.denominator]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"game-controls\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"hint-button\",onClick:toggleHint,children:[\"\\uD83D\\uDCA1 \",gameState.showHint?'Skrýt nápovědu':'Zobrazit nápovědu']}),/*#__PURE__*/_jsx(\"button\",{className:\"reset-button\",onClick:resetGame,children:\"\\uD83D\\uDD04 Nov\\xE1 hra\"})]})]}),/*#__PURE__*/_jsxs(\"main\",{className:\"game-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"instructions\",children:[gameState.gamePhase==='cutting'&&/*#__PURE__*/_jsx(\"p\",{children:\"\\uD83D\\uDD2A Klikni na n\\u016F\\u017E pro \\u0159ez\\xE1n\\xED pizzy na v\\xEDce d\\xEDlk\\u016F!\"}),gameState.gamePhase==='selecting'&&/*#__PURE__*/_jsx(\"p\",{children:\"\\uD83D\\uDC46 Klikni na d\\xEDlky pizzy pro v\\xFDb\\u011Br spr\\xE1vn\\xE9ho mno\\u017Estv\\xED!\"}),gameState.gamePhase==='complete'&&/*#__PURE__*/_jsx(\"p\",{children:\"\\uD83C\\uDF89 V\\xFDborn\\u011B! Klikni na \\\"Dal\\u0161\\xED kolo\\\" pro pokra\\u010Dov\\xE1n\\xED.\"}),gameState.showHint&&/*#__PURE__*/_jsxs(\"div\",{className:\"hint\",children:[\"\\uD83D\\uDCA1 \",/*#__PURE__*/_jsx(\"strong\",{children:\"N\\xE1pov\\u011Bda:\"}),\" Pro zlomek \",gameState.targetFraction.numerator,\"/\",gameState.targetFraction.denominator,\"pot\\u0159ebuje\\u0161 rozd\\u011Blit pizzu na \",gameState.targetFraction.denominator,\" d\\xEDlk\\u016F a vybrat \",gameState.targetFraction.numerator,\" z nich.\"]}),gameState.feedback&&/*#__PURE__*/_jsx(\"div\",{className:\"feedback \".concat(gameState.feedback.includes('❌')?'error':'success'),children:gameState.feedback})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"game-area\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"knife-section\",children:/*#__PURE__*/_jsx(\"button\",{className:\"knife-button\",onClick:cutPizza,disabled:gameState.gamePhase!=='cutting',children:\"\\uD83D\\uDD2A \\u0158ezat pizzu\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"pizza-container\",children:/*#__PURE__*/_jsx(PizzaSVG,{totalSlices:gameState.totalSlices,selectedSlices:gameState.selectedSlices,onSliceClick:toggleSlice})}),/*#__PURE__*/_jsxs(\"div\",{className:\"controls\",children:[gameState.gamePhase==='selecting'&&/*#__PURE__*/_jsx(\"button\",{className:\"submit-button\",onClick:submitAnswer,children:\"\\u2705 Potvrdit odpov\\u011B\\u010F\"}),gameState.gamePhase==='complete'&&/*#__PURE__*/_jsx(\"button\",{className:\"next-round-button\",onClick:startNewRound,children:\"\\u27A1\\uFE0F Dal\\u0161\\xED kolo\"})]})]})]})]});};export default PizzaFractionGame;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PizzaSVG", "soundEffects", "jsx", "_jsx", "jsxs", "_jsxs", "PizzaFractionGame", "gameState", "setGameState", "targetFraction", "numerator", "denominator", "currentFraction", "score", "gamePhase", "selectedSlices", "totalSlices", "feedback", "showHint", "generateRandomFraction", "easyFractions", "Math", "floor", "random", "length", "startNewRound", "newFraction", "resetGame", "toggleHint", "prev", "_objectSpread", "cutPizza", "playSliceSound", "newTotalSlices", "Array", "fill", "toggleSlice", "index", "playSelectSound", "newSelectedSlices", "selectedCount", "filter", "Boolean", "checkAnswer", "gcd", "a", "b", "simplifyFraction", "frac", "divisor", "simplifiedTarget", "simplifiedCurrent", "submitAnswer", "isCorrect", "playSuccessSound", "playErrorSound", "className", "children", "onClick", "concat", "includes", "disabled", "onSliceClick"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/PizzaFractionGame.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './PizzaFractionGame.css';\nimport PizzaSVG from './PizzaSVG';\nimport { soundEffects } from './SoundEffects';\n\ninterface Fraction {\n  numerator: number;\n  denominator: number;\n}\n\ninterface GameState {\n  targetFraction: Fraction;\n  currentFraction: Fraction;\n  score: number;\n  gamePhase: 'cutting' | 'selecting' | 'complete';\n  selectedSlices: boolean[];\n  totalSlices: number;\n  feedback: string;\n  showHint: boolean;\n}\n\nconst PizzaFractionGame: React.FC = () => {\n  const [gameState, setGameState] = useState<GameState>({\n    targetFraction: { numerator: 1, denominator: 2 },\n    currentFraction: { numerator: 0, denominator: 1 },\n    score: 0,\n    gamePhase: 'cutting',\n    selectedSlices: [],\n    totalSlices: 1,\n    feedback: '',\n    showHint: false\n  });\n\n  // Generuje náhodný zlomek menší než 1, v<PERSON>dný pro děti\n  const generateRandomFraction = (): Fraction => {\n    // Začneme s jednodu<PERSON><PERSON><PERSON><PERSON> pro mlad<PERSON><PERSON> děti\n    const easyFractions = [\n      { numerator: 1, denominator: 2 },\n      { numerator: 1, denominator: 3 },\n      { numerator: 2, denominator: 3 },\n      { numerator: 1, denominator: 4 },\n      { numerator: 3, denominator: 4 },\n      { numerator: 1, denominator: 5 },\n      { numerator: 2, denominator: 5 },\n      { numerator: 3, denominator: 5 },\n      { numerator: 4, denominator: 5 },\n      { numerator: 1, denominator: 6 },\n      { numerator: 5, denominator: 6 },\n      { numerator: 1, denominator: 8 },\n      { numerator: 3, denominator: 8 },\n      { numerator: 5, denominator: 8 },\n      { numerator: 7, denominator: 8 }\n    ];\n\n    return easyFractions[Math.floor(Math.random() * easyFractions.length)];\n  };\n\n  // Spustí novou hru\n  const startNewRound = () => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: { numerator: 0, denominator: 1 },\n      score: gameState.score,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  };\n\n  // Resetuje celou hru\n  const resetGame = () => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: { numerator: 0, denominator: 1 },\n      score: 0,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  };\n\n  // Zobrazí/skryje nápovědu\n  const toggleHint = () => {\n    setGameState(prev => ({ ...prev, showHint: !prev.showHint }));\n  };\n\n  // Řezání pizzy (zvyšuje počet dílků)\n  const cutPizza = () => {\n    if (gameState.gamePhase === 'cutting') {\n      soundEffects.playSliceSound();\n      const newTotalSlices = gameState.totalSlices + 1;\n      setGameState(prev => ({\n        ...prev,\n        totalSlices: newTotalSlices,\n        currentFraction: { ...prev.currentFraction, denominator: newTotalSlices },\n        selectedSlices: new Array(newTotalSlices).fill(false)\n      }));\n    }\n  };\n\n  // Výběr/zrušení výběru dílku pizzy\n  const toggleSlice = (index: number) => {\n    if (gameState.gamePhase === 'cutting') {\n      setGameState(prev => ({ ...prev, gamePhase: 'selecting' }));\n    }\n\n    if (gameState.gamePhase === 'selecting') {\n      soundEffects.playSelectSound();\n      const newSelectedSlices = [...gameState.selectedSlices];\n      newSelectedSlices[index] = !newSelectedSlices[index];\n      const selectedCount = newSelectedSlices.filter(Boolean).length;\n\n      setGameState(prev => ({\n        ...prev,\n        selectedSlices: newSelectedSlices,\n        currentFraction: { numerator: selectedCount, denominator: prev.totalSlices }\n      }));\n    }\n  };\n\n  // Kontrola správnosti odpovědi\n  const checkAnswer = () => {\n    const { targetFraction, currentFraction } = gameState;\n    \n    // Zjednodušení zlomků pro porovnání\n    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);\n    \n    const simplifyFraction = (frac: Fraction): Fraction => {\n      const divisor = gcd(frac.numerator, frac.denominator);\n      return {\n        numerator: frac.numerator / divisor,\n        denominator: frac.denominator / divisor\n      };\n    };\n    \n    const simplifiedTarget = simplifyFraction(targetFraction);\n    const simplifiedCurrent = simplifyFraction(currentFraction);\n    \n    return simplifiedTarget.numerator === simplifiedCurrent.numerator && \n           simplifiedTarget.denominator === simplifiedCurrent.denominator;\n  };\n\n  // Potvrzení odpovědi\n  const submitAnswer = () => {\n    if (gameState.gamePhase === 'selecting') {\n      const isCorrect = checkAnswer();\n      if (isCorrect) {\n        soundEffects.playSuccessSound();\n        setGameState(prev => ({\n          ...prev,\n          score: prev.score + 1,\n          gamePhase: 'complete',\n          feedback: '🎉 Výborně! Správná odpověď!'\n        }));\n      } else {\n        soundEffects.playErrorSound();\n        // Špatná odpověď - resetuj výběr\n        setGameState(prev => ({\n          ...prev,\n          selectedSlices: new Array(prev.totalSlices).fill(false),\n          currentFraction: { numerator: 0, denominator: prev.totalSlices },\n          feedback: '❌ Zkus to znovu! Zkontroluj si zlomek.'\n        }));\n      }\n    }\n  };\n\n  // Inicializace první hry\n  useEffect(() => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: { numerator: 0, denominator: 1 },\n      score: 0,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  }, []);\n\n  return (\n    <div className=\"pizza-game\">\n      <header className=\"game-header\">\n        <h1>🍕 Pizza Zlomky</h1>\n        <div className=\"game-info\">\n          <div className=\"score\">Skóre: {gameState.score}</div>\n          <div className=\"target-fraction\">\n            Cíl: {gameState.targetFraction.numerator}/{gameState.targetFraction.denominator}\n          </div>\n          <div className=\"current-fraction\">\n            Tvůj zlomek: {gameState.currentFraction.numerator}/{gameState.currentFraction.denominator}\n          </div>\n        </div>\n        <div className=\"game-controls\">\n          <button className=\"hint-button\" onClick={toggleHint}>\n            💡 {gameState.showHint ? 'Skrýt nápovědu' : 'Zobrazit nápovědu'}\n          </button>\n          <button className=\"reset-button\" onClick={resetGame}>\n            🔄 Nová hra\n          </button>\n        </div>\n      </header>\n\n      <main className=\"game-content\">\n        <div className=\"instructions\">\n          {gameState.gamePhase === 'cutting' && (\n            <p>🔪 Klikni na nůž pro řezání pizzy na více dílků!</p>\n          )}\n          {gameState.gamePhase === 'selecting' && (\n            <p>👆 Klikni na dílky pizzy pro výběr správného množství!</p>\n          )}\n          {gameState.gamePhase === 'complete' && (\n            <p>🎉 Výborně! Klikni na \"Další kolo\" pro pokračování.</p>\n          )}\n          {gameState.showHint && (\n            <div className=\"hint\">\n              💡 <strong>Nápověda:</strong> Pro zlomek {gameState.targetFraction.numerator}/{gameState.targetFraction.denominator}\n              potřebuješ rozdělit pizzu na {gameState.targetFraction.denominator} dílků\n              a vybrat {gameState.targetFraction.numerator} z nich.\n            </div>\n          )}\n          {gameState.feedback && (\n            <div className={`feedback ${gameState.feedback.includes('❌') ? 'error' : 'success'}`}>\n              {gameState.feedback}\n            </div>\n          )}\n        </div>\n\n        <div className=\"game-area\">\n          <div className=\"knife-section\">\n            <button \n              className=\"knife-button\"\n              onClick={cutPizza}\n              disabled={gameState.gamePhase !== 'cutting'}\n            >\n              🔪 Řezat pizzu\n            </button>\n          </div>\n\n          <div className=\"pizza-container\">\n            <PizzaSVG\n              totalSlices={gameState.totalSlices}\n              selectedSlices={gameState.selectedSlices}\n              onSliceClick={toggleSlice}\n            />\n          </div>\n\n          <div className=\"controls\">\n            {gameState.gamePhase === 'selecting' && (\n              <button className=\"submit-button\" onClick={submitAnswer}>\n                ✅ Potvrdit odpověď\n              </button>\n            )}\n            {gameState.gamePhase === 'complete' && (\n              <button className=\"next-round-button\" onClick={startNewRound}>\n                ➡️ Další kolo\n              </button>\n            )}\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default PizzaFractionGame;\n"], "mappings": "iIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,yBAAyB,CAChC,MAAO,CAAAC,QAAQ,KAAM,YAAY,CACjC,OAASC,YAAY,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAkB9C,KAAM,CAAAC,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGV,QAAQ,CAAY,CACpDW,cAAc,CAAE,CAAEC,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChDC,eAAe,CAAE,CAAEF,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACjDE,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,SAAS,CACpBC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,KACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAgB,CAC7C;AACA,KAAM,CAAAC,aAAa,CAAG,CACpB,CAAEV,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChC,CAAED,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACjC,CAED,MAAO,CAAAS,aAAa,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGH,aAAa,CAACI,MAAM,CAAC,CAAC,CACxE,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,WAAW,CAAGP,sBAAsB,CAAC,CAAC,CAC5CX,YAAY,CAAC,CACXC,cAAc,CAAEiB,WAAW,CAC3Bd,eAAe,CAAE,CAAEF,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACjDE,KAAK,CAAEN,SAAS,CAACM,KAAK,CACtBC,SAAS,CAAE,SAAS,CACpBC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,KACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAS,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAD,WAAW,CAAGP,sBAAsB,CAAC,CAAC,CAC5CX,YAAY,CAAC,CACXC,cAAc,CAAEiB,WAAW,CAC3Bd,eAAe,CAAE,CAAEF,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACjDE,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,SAAS,CACpBC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,KACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAU,UAAU,CAAGA,CAAA,GAAM,CACvBpB,YAAY,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEX,QAAQ,CAAE,CAACW,IAAI,CAACX,QAAQ,EAAG,CAAC,CAC/D,CAAC,CAED;AACA,KAAM,CAAAa,QAAQ,CAAGA,CAAA,GAAM,CACrB,GAAIxB,SAAS,CAACO,SAAS,GAAK,SAAS,CAAE,CACrCb,YAAY,CAAC+B,cAAc,CAAC,CAAC,CAC7B,KAAM,CAAAC,cAAc,CAAG1B,SAAS,CAACS,WAAW,CAAG,CAAC,CAChDR,YAAY,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPb,WAAW,CAAEiB,cAAc,CAC3BrB,eAAe,CAAAkB,aAAA,CAAAA,aAAA,IAAOD,IAAI,CAACjB,eAAe,MAAED,WAAW,CAAEsB,cAAc,EAAE,CACzElB,cAAc,CAAE,GAAI,CAAAmB,KAAK,CAACD,cAAc,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC,EACrD,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAIC,KAAa,EAAK,CACrC,GAAI9B,SAAS,CAACO,SAAS,GAAK,SAAS,CAAE,CACrCN,YAAY,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEf,SAAS,CAAE,WAAW,EAAG,CAAC,CAC7D,CAEA,GAAIP,SAAS,CAACO,SAAS,GAAK,WAAW,CAAE,CACvCb,YAAY,CAACqC,eAAe,CAAC,CAAC,CAC9B,KAAM,CAAAC,iBAAiB,CAAG,CAAC,GAAGhC,SAAS,CAACQ,cAAc,CAAC,CACvDwB,iBAAiB,CAACF,KAAK,CAAC,CAAG,CAACE,iBAAiB,CAACF,KAAK,CAAC,CACpD,KAAM,CAAAG,aAAa,CAAGD,iBAAiB,CAACE,MAAM,CAACC,OAAO,CAAC,CAAClB,MAAM,CAE9DhB,YAAY,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPd,cAAc,CAAEwB,iBAAiB,CACjC3B,eAAe,CAAE,CAAEF,SAAS,CAAE8B,aAAa,CAAE7B,WAAW,CAAEkB,IAAI,CAACb,WAAY,CAAC,EAC5E,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAA2B,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAElC,cAAc,CAAEG,eAAgB,CAAC,CAAGL,SAAS,CAErD;AACA,KAAM,CAAAqC,GAAG,CAAGA,CAACC,CAAS,CAAEC,CAAS,GAAaA,CAAC,GAAK,CAAC,CAAGD,CAAC,CAAGD,GAAG,CAACE,CAAC,CAAED,CAAC,CAAGC,CAAC,CAAC,CAEzE,KAAM,CAAAC,gBAAgB,CAAIC,IAAc,EAAe,CACrD,KAAM,CAAAC,OAAO,CAAGL,GAAG,CAACI,IAAI,CAACtC,SAAS,CAAEsC,IAAI,CAACrC,WAAW,CAAC,CACrD,MAAO,CACLD,SAAS,CAAEsC,IAAI,CAACtC,SAAS,CAAGuC,OAAO,CACnCtC,WAAW,CAAEqC,IAAI,CAACrC,WAAW,CAAGsC,OAClC,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGH,gBAAgB,CAACtC,cAAc,CAAC,CACzD,KAAM,CAAA0C,iBAAiB,CAAGJ,gBAAgB,CAACnC,eAAe,CAAC,CAE3D,MAAO,CAAAsC,gBAAgB,CAACxC,SAAS,GAAKyC,iBAAiB,CAACzC,SAAS,EAC1DwC,gBAAgB,CAACvC,WAAW,GAAKwC,iBAAiB,CAACxC,WAAW,CACvE,CAAC,CAED;AACA,KAAM,CAAAyC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI7C,SAAS,CAACO,SAAS,GAAK,WAAW,CAAE,CACvC,KAAM,CAAAuC,SAAS,CAAGV,WAAW,CAAC,CAAC,CAC/B,GAAIU,SAAS,CAAE,CACbpD,YAAY,CAACqD,gBAAgB,CAAC,CAAC,CAC/B9C,YAAY,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPhB,KAAK,CAAEgB,IAAI,CAAChB,KAAK,CAAG,CAAC,CACrBC,SAAS,CAAE,UAAU,CACrBG,QAAQ,CAAE,8BAA8B,EACxC,CAAC,CACL,CAAC,IAAM,CACLhB,YAAY,CAACsD,cAAc,CAAC,CAAC,CAC7B;AACA/C,YAAY,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPd,cAAc,CAAE,GAAI,CAAAmB,KAAK,CAACL,IAAI,CAACb,WAAW,CAAC,CAACmB,IAAI,CAAC,KAAK,CAAC,CACvDvB,eAAe,CAAE,CAAEF,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAEkB,IAAI,CAACb,WAAY,CAAC,CAChEC,QAAQ,CAAE,wCAAwC,EAClD,CAAC,CACL,CACF,CACF,CAAC,CAED;AACAlB,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2B,WAAW,CAAGP,sBAAsB,CAAC,CAAC,CAC5CX,YAAY,CAAC,CACXC,cAAc,CAAEiB,WAAW,CAC3Bd,eAAe,CAAE,CAAEF,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACjDE,KAAK,CAAE,CAAC,CACRC,SAAS,CAAE,SAAS,CACpBC,cAAc,CAAE,EAAE,CAClBC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,KACZ,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEb,KAAA,QAAKmD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpD,KAAA,WAAQmD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC7BtD,IAAA,OAAAsD,QAAA,CAAI,2BAAe,CAAI,CAAC,cACxBpD,KAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpD,KAAA,QAAKmD,SAAS,CAAC,OAAO,CAAAC,QAAA,EAAC,YAAO,CAAClD,SAAS,CAACM,KAAK,EAAM,CAAC,cACrDR,KAAA,QAAKmD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,UAC1B,CAAClD,SAAS,CAACE,cAAc,CAACC,SAAS,CAAC,GAAC,CAACH,SAAS,CAACE,cAAc,CAACE,WAAW,EAC5E,CAAC,cACNN,KAAA,QAAKmD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,oBACnB,CAAClD,SAAS,CAACK,eAAe,CAACF,SAAS,CAAC,GAAC,CAACH,SAAS,CAACK,eAAe,CAACD,WAAW,EACtF,CAAC,EACH,CAAC,cACNN,KAAA,QAAKmD,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpD,KAAA,WAAQmD,SAAS,CAAC,aAAa,CAACE,OAAO,CAAE9B,UAAW,CAAA6B,QAAA,EAAC,eAChD,CAAClD,SAAS,CAACW,QAAQ,CAAG,gBAAgB,CAAG,mBAAmB,EACzD,CAAC,cACTf,IAAA,WAAQqD,SAAS,CAAC,cAAc,CAACE,OAAO,CAAE/B,SAAU,CAAA8B,QAAA,CAAC,0BAErD,CAAQ,CAAC,EACN,CAAC,EACA,CAAC,cAETpD,KAAA,SAAMmD,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5BpD,KAAA,QAAKmD,SAAS,CAAC,cAAc,CAAAC,QAAA,EAC1BlD,SAAS,CAACO,SAAS,GAAK,SAAS,eAChCX,IAAA,MAAAsD,QAAA,CAAG,4FAAgD,CAAG,CACvD,CACAlD,SAAS,CAACO,SAAS,GAAK,WAAW,eAClCX,IAAA,MAAAsD,QAAA,CAAG,2FAAsD,CAAG,CAC7D,CACAlD,SAAS,CAACO,SAAS,GAAK,UAAU,eACjCX,IAAA,MAAAsD,QAAA,CAAG,4FAAmD,CAAG,CAC1D,CACAlD,SAAS,CAACW,QAAQ,eACjBb,KAAA,QAAKmD,SAAS,CAAC,MAAM,CAAAC,QAAA,EAAC,eACjB,cAAAtD,IAAA,WAAAsD,QAAA,CAAQ,mBAAS,CAAQ,CAAC,eAAY,CAAClD,SAAS,CAACE,cAAc,CAACC,SAAS,CAAC,GAAC,CAACH,SAAS,CAACE,cAAc,CAACE,WAAW,CAAC,8CACvF,CAACJ,SAAS,CAACE,cAAc,CAACE,WAAW,CAAC,0BAC1D,CAACJ,SAAS,CAACE,cAAc,CAACC,SAAS,CAAC,UAC/C,EAAK,CACN,CACAH,SAAS,CAACU,QAAQ,eACjBd,IAAA,QAAKqD,SAAS,aAAAG,MAAA,CAAcpD,SAAS,CAACU,QAAQ,CAAC2C,QAAQ,CAAC,GAAG,CAAC,CAAG,OAAO,CAAG,SAAS,CAAG,CAAAH,QAAA,CAClFlD,SAAS,CAACU,QAAQ,CAChB,CACN,EACE,CAAC,cAENZ,KAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBtD,IAAA,QAAKqD,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BtD,IAAA,WACEqD,SAAS,CAAC,cAAc,CACxBE,OAAO,CAAE3B,QAAS,CAClB8B,QAAQ,CAAEtD,SAAS,CAACO,SAAS,GAAK,SAAU,CAAA2C,QAAA,CAC7C,+BAED,CAAQ,CAAC,CACN,CAAC,cAENtD,IAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BtD,IAAA,CAACH,QAAQ,EACPgB,WAAW,CAAET,SAAS,CAACS,WAAY,CACnCD,cAAc,CAAER,SAAS,CAACQ,cAAe,CACzC+C,YAAY,CAAE1B,WAAY,CAC3B,CAAC,CACC,CAAC,cAEN/B,KAAA,QAAKmD,SAAS,CAAC,UAAU,CAAAC,QAAA,EACtBlD,SAAS,CAACO,SAAS,GAAK,WAAW,eAClCX,IAAA,WAAQqD,SAAS,CAAC,eAAe,CAACE,OAAO,CAAEN,YAAa,CAAAK,QAAA,CAAC,mCAEzD,CAAQ,CACT,CACAlD,SAAS,CAACO,SAAS,GAAK,UAAU,eACjCX,IAAA,WAAQqD,SAAS,CAAC,mBAAmB,CAACE,OAAO,CAAEjC,aAAc,CAAAgC,QAAA,CAAC,iCAE9D,CAAQ,CACT,EACE,CAAC,EACH,CAAC,EACF,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}