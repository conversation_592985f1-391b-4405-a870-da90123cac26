import React from 'react';

interface PizzaChefProps {
  className?: string;
}

const PizzaChef: React.FC<PizzaChefProps> = ({ className = '' }) => {
  return (
    <div className={`pizza-chef ${className}`}>
      <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
        {/* Pozadí - cihlové pozadí */}
        <rect x="0" y="0" width="120" height="120" fill="#d2691e" rx="10"/>

        {/* Cihlové vzory */}
        <g stroke="#8b4513" strokeWidth="1" fill="none">
          <line x1="0" y1="15" x2="120" y2="15"/>
          <line x1="0" y1="30" x2="120" y2="30"/>
          <line x1="0" y1="45" x2="120" y2="45"/>
          <line x1="0" y1="60" x2="120" y2="60"/>
          <line x1="0" y1="75" x2="120" y2="75"/>
          <line x1="0" y1="90" x2="120" y2="90"/>
          <line x1="30" y1="0" x2="30" y2="15"/>
          <line x1="60" y1="15" x2="60" y2="30"/>
          <line x1="90" y1="30" x2="90" y2="45"/>
          <line x1="30" y1="45" x2="30" y2="60"/>
          <line x1="60" y1="60" x2="60" y2="75"/>
          <line x1="90" y1="75" x2="90" y2="90"/>
        </g>

        {/* Pec v pozadí */}
        <ellipse cx="90" cy="40" rx="25" ry="20" fill="#8b4513" stroke="#654321" strokeWidth="2"/>
        <ellipse cx="90" cy="40" rx="20" ry="15" fill="#2c1810"/>

        {/* Oheň v peci */}
        <g transform="translate(90, 40)">
          <ellipse cx="0" cy="0" rx="8" ry="6" fill="#ff6b35"/>
          <ellipse cx="-3" cy="-2" rx="4" ry="8" fill="#ff8c42"/>
          <ellipse cx="3" cy="-1" rx="3" ry="6" fill="#ffd23f"/>
          <ellipse cx="0" cy="-3" rx="2" ry="4" fill="#fff"/>
        </g>

        {/* Kuchařská čepice */}
        <ellipse cx="45" cy="25" rx="18" ry="12" fill="#ffffff" stroke="#ddd" strokeWidth="1"/>
        <path d="M 27 25 Q 45 8 63 25 Q 63 18 45 15 Q 27 18 27 25" fill="#ffffff" stroke="#ddd" strokeWidth="1"/>

        {/* Obličej */}
        <ellipse cx="45" cy="45" rx="15" ry="18" fill="#fdbcb4"/>

        {/* Oči */}
        <ellipse cx="40" cy="40" rx="2.5" ry="3" fill="#2c3e50"/>
        <ellipse cx="50" cy="40" rx="2.5" ry="3" fill="#2c3e50"/>
        <ellipse cx="40" cy="39" rx="1" ry="1.5" fill="#ffffff"/>
        <ellipse cx="50" cy="39" rx="1" ry="1.5" fill="#ffffff"/>

        {/* Nos */}
        <ellipse cx="45" cy="45" rx="2" ry="3" fill="#f39c12"/>

        {/* Úsměv */}
        <path d="M 38 50 Q 45 58 52 50" stroke="#e74c3c" strokeWidth="2" fill="#e74c3c" strokeLinecap="round"/>

        {/* Zuby */}
        <rect x="42" y="52" width="1.5" height="2" fill="#ffffff" rx="0.5"/>
        <rect x="44" y="52" width="1.5" height="2" fill="#ffffff" rx="0.5"/>
        <rect x="46" y="52" width="1.5" height="2" fill="#ffffff" rx="0.5"/>

        {/* Knír */}
        <path d="M 38 48 Q 42 46 45 48 Q 48 46 52 48" stroke="#8b4513" strokeWidth="2" fill="none" strokeLinecap="round"/>

        {/* Obočí */}
        <path d="M 36 36 Q 40 34 44 36" stroke="#8b4513" strokeWidth="1.5" fill="none" strokeLinecap="round"/>
        <path d="M 46 36 Q 50 34 54 36" stroke="#8b4513" strokeWidth="1.5" fill="none" strokeLinecap="round"/>

        {/* Červený šátek na krku */}
        <path d="M 32 60 Q 45 65 58 60 Q 58 68 45 70 Q 32 68 32 60" fill="#e74c3c" stroke="#c0392b" strokeWidth="1"/>

        {/* Bílá košile */}
        <ellipse cx="45" cy="80" rx="18" ry="15" fill="#ffffff" stroke="#ddd" strokeWidth="1"/>

        {/* Knoflíky */}
        <circle cx="45" cy="75" r="1.5" fill="#3498db"/>
        <circle cx="45" cy="82" r="1.5" fill="#3498db"/>
        <circle cx="45" cy="89" r="1.5" fill="#3498db"/>

        {/* Levá ruka držící pizzu */}
        <ellipse cx="25" cy="70" rx="6" ry="3" fill="#fdbcb4" transform="rotate(-10 25 70)"/>

        {/* Pizza v ruce */}
        <g transform="translate(18, 65) rotate(-10)">
          <circle cx="0" cy="0" r="8" fill="#f39c12" stroke="#d68910" strokeWidth="1"/>
          {/* Posyp na pizze */}
          <circle cx="-2" cy="-2" r="1" fill="#e74c3c"/>
          <circle cx="2" cy="-1" r="0.8" fill="#27ae60"/>
          <circle cx="-1" cy="2" r="0.8" fill="#f1c40f"/>
          <circle cx="3" cy="2" r="1" fill="#e74c3c"/>
          <circle cx="0" cy="0" r="0.8" fill="#27ae60"/>
          <ellipse cx="-3" cy="1" rx="1.5" ry="0.8" fill="#8e44ad"/>

          {/* Animace pizzy - mírné kývání */}
          <animateTransform
            attributeName="transform"
            type="rotate"
            values="0 0 0;3 0 0;0 0 0;-3 0 0;0 0 0"
            dur="2.5s"
            repeatCount="indefinite"
          />
        </g>

        {/* Pravá ruka */}
        <ellipse cx="65" cy="75" rx="5" ry="3" fill="#fdbcb4" transform="rotate(15 65 75)"/>

        {/* Animace - mírné kývání */}
        <animateTransform
          attributeName="transform"
          type="rotate"
          values="0 60 60;1 60 60;0 60 60;-1 60 60;0 60 60"
          dur="4s"
          repeatCount="indefinite"
        />
      </svg>
    </div>
  );
};

export default PizzaChef;
