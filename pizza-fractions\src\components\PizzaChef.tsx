import React from 'react';

interface PizzaChefProps {
  className?: string;
}

const PizzaChef: React.FC<PizzaChefProps> = ({ className = '' }) => {
  return (
    <div className={`pizza-chef ${className}`}>
      <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
        {/* Pozadí - žlutý kruh */}
        <circle cx="60" cy="60" r="55" fill="#f1c40f" stroke="#f39c12" strokeWidth="3"/>
        
        {/* Kuchařská čepice */}
        <ellipse cx="60" cy="35" rx="25" ry="15" fill="#ffffff" stroke="#ddd" strokeWidth="1"/>
        <path d="M 35 35 Q 60 15 85 35 Q 85 25 60 20 Q 35 25 35 35" fill="#ffffff" stroke="#ddd" strokeWidth="1"/>
        
        {/* Obličej */}
        <circle cx="60" cy="55" r="18" fill="#fdbcb4" stroke="#f39c12" strokeWidth="1"/>
        
        {/* Oči */}
        <ellipse cx="54" cy="50" rx="3" ry="4" fill="#2c3e50"/>
        <ellipse cx="66" cy="50" rx="3" ry="4" fill="#2c3e50"/>
        <ellipse cx="54" cy="49" rx="1" ry="1.5" fill="#ffffff"/>
        <ellipse cx="66" cy="49" rx="1" ry="1.5" fill="#ffffff"/>
        
        {/* Nos */}
        <ellipse cx="60" cy="55" rx="2" ry="3" fill="#f39c12"/>
        
        {/* Úsměv */}
        <path d="M 52 60 Q 60 68 68 60" stroke="#e74c3c" strokeWidth="2" fill="none" strokeLinecap="round"/>

        {/* Zuby */}
        <rect x="57" y="62" width="2" height="3" fill="#ffffff" rx="1"/>
        <rect x="60" y="62" width="2" height="3" fill="#ffffff" rx="1"/>
        <rect x="63" y="62" width="2" height="3" fill="#ffffff" rx="1"/>

        {/* Knír */}
        <path d="M 50 58 Q 55 56 60 58 Q 65 56 70 58" stroke="#8b4513" strokeWidth="2" fill="none" strokeLinecap="round"/>

        {/* Obočí */}
        <path d="M 50 46 Q 54 44 58 46" stroke="#8b4513" strokeWidth="1.5" fill="none" strokeLinecap="round"/>
        <path d="M 62 46 Q 66 44 70 46" stroke="#8b4513" strokeWidth="1.5" fill="none" strokeLinecap="round"/>
        
        {/* Červený šátek na krku */}
        <path d="M 45 70 Q 60 75 75 70 Q 75 80 60 82 Q 45 80 45 70" fill="#e74c3c" stroke="#c0392b" strokeWidth="1"/>
        
        {/* Bílá košile */}
        <ellipse cx="60" cy="85" rx="20" ry="15" fill="#ffffff" stroke="#ddd" strokeWidth="1"/>
        
        {/* Knoflíky */}
        <circle cx="60" cy="80" r="2" fill="#3498db"/>
        <circle cx="60" cy="88" r="2" fill="#3498db"/>
        
        {/* Levá ruka držící pizzu */}
        <ellipse cx="35" cy="75" rx="8" ry="4" fill="#fdbcb4" transform="rotate(-20 35 75)"/>
        
        {/* Pizza v ruce */}
        <g transform="translate(25, 65) rotate(-15)">
          <circle cx="0" cy="0" r="12" fill="#f39c12" stroke="#d68910" strokeWidth="1"/>
          {/* Posyp na pizze */}
          <circle cx="-4" cy="-3" r="1.5" fill="#e74c3c"/>
          <circle cx="3" cy="-2" r="1" fill="#27ae60"/>
          <circle cx="-2" cy="4" r="1" fill="#f1c40f"/>
          <circle cx="5" cy="3" r="1.5" fill="#e74c3c"/>
          <circle cx="0" cy="0" r="1" fill="#27ae60"/>
          <circle cx="-6" cy="2" r="1" fill="#8e44ad"/>

          {/* Animace pizzy - mírné kývání */}
          <animateTransform
            attributeName="transform"
            type="rotate"
            values="0 0 0;5 0 0;0 0 0;-5 0 0;0 0 0"
            dur="2s"
            repeatCount="indefinite"
          />
        </g>
        
        {/* Pravá ruka */}
        <ellipse cx="85" cy="80" rx="6" ry="4" fill="#fdbcb4" transform="rotate(20 85 80)"/>
        
        {/* Animace - mírné kývání */}
        <animateTransform
          attributeName="transform"
          type="rotate"
          values="0 60 60;2 60 60;0 60 60;-2 60 60;0 60 60"
          dur="3s"
          repeatCount="indefinite"
        />
      </svg>
    </div>
  );
};

export default PizzaChef;
