# 🍕 Pizza Zlomky - Vzdělávací hra pro děti

Interaktivní hra pro výuku zlomků určená pro děti ve věku kolem 10 let. Hra funguje jak na interaktivních projektorech ve třídách, tak na mobilních zařízeních.

## 🎮 Jak hrát

1. **Řezání pizzy**: Klikni na nůž pro rozdělení pizzy na více dílků
2. **Výb<PERSON><PERSON> dílk<PERSON>**: Klikni na jednotlivé kousky pizzy pro výběr správného množství
3. **Potvrzení**: Klikni na "Potvrdit odpověď" pro kontrolu
4. **Dalš<PERSON> kolo**: Po správné odpovědi pokračuj dalším kolem

## 🎯 Cíl hry

Děti se učí:
- Rozpoznávat zlomky
- Chápat vztah mezi čitatelem a jmenovatelem
- Vizualizovat zlomky pomocí pizzy
- Trénovat matematické dovednosti zábavnou formou

## 🛠️ Technické informace

Hra je vytvořena v React TypeScript a obsahuje:
- Responzivní design pro různá zařízení
- SVG grafiku pro přesné zobrazení
- Zvukové efekty pro lepší zážitek
- Systém nápovědy
- Možnost resetování hry

## 🚀 Spuštění aplikace

### Požadavky
- Node.js (verze 14 nebo vyšší)
- npm nebo yarn

### Instalace a spuštění

```bash
# Instalace závislostí
npm install

# Spuštění vývojového serveru
npm start
```

Aplikace se otevře na [http://localhost:3000](http://localhost:3000).

### Další příkazy

```bash
# Spuštění testů
npm test

# Sestavení pro produkci
npm run build
```

## 📱 Kompatibilita

- ✅ Moderní webové prohlížeče
- ✅ Mobilní zařízení (telefony, tablety)
- ✅ Interaktivní projektory
- ✅ Touch screen zařízení

## 🎨 Vizuální styl

Hra používá:
- Barevný gradient pozadí
- Přátelské fonty (Comic Sans MS)
- Velká tlačítka vhodná pro děti
- Animace a zvukové efekty
- Emoji pro lepší orientaci

## 🔧 Struktura projektu

```
src/
├── components/
│   ├── PizzaFractionGame.tsx    # Hlavní komponenta hry
│   ├── PizzaFractionGame.css    # Styly pro hru
│   ├── PizzaSVG.tsx            # SVG komponenta pizzy
│   └── SoundEffects.tsx        # Zvukové efekty
├── App.tsx                     # Hlavní aplikace
└── index.tsx                   # Vstupní bod
```

## 📚 Vzdělávací hodnota

Hra pomáhá dětem:
- Pochopit koncept zlomků
- Rozvíjet vizuální představivost
- Trénovat jemnou motoriku (klikání)
- Získat okamžitou zpětnou vazbu
- Učit se hravou formou

## 🎵 Zvukové efekty

- 🔪 Zvuk řezání při dělení pizzy
- 👆 Zvuk výběru při klikání na dílky
- ✅ Melodie při správné odpovědi
- ❌ Zvuk při chybné odpovědi

## 💡 Nápověda

Hra obsahuje systém nápovědy, který dětem vysvětlí:
- Kolik dílků potřebují vytvořit
- Kolik dílků mají vybrat
- Jak dosáhnout cílového zlomku

---

Vytvořeno s ❤️ pro vzdělávání dětí
