{"ast": null, "code": "var _jsxFileName = \"C:\\\\Work\\\\new_AI\\\\PizzaFraction\\\\pizza-fractions\\\\src\\\\components\\\\PizzaFractionGame.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './PizzaFractionGame.css';\nimport PizzaSVG from './PizzaSVG';\nimport { soundEffects } from './SoundEffects';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PizzaFractionGame = () => {\n  _s();\n  const [gameState, setGameState] = useState({\n    targetFraction: {\n      numerator: 1,\n      denominator: 2\n    },\n    currentFraction: {\n      numerator: 0,\n      denominator: 1\n    },\n    score: 0,\n    gamePhase: 'cutting',\n    selectedSlices: [],\n    totalSlices: 1,\n    feedback: '',\n    showHint: false\n  });\n\n  // Generuje náhodný zlomek menší než 1, v<PERSON>dn<PERSON> pro děti\n  const generateRandomFraction = () => {\n    // <PERSON>ačneme s jedno<PERSON><PERSON><PERSON><PERSON><PERSON> pro mlad<PERSON><PERSON> děti\n    const easyFractions = [{\n      numerator: 1,\n      denominator: 2\n    }, {\n      numerator: 1,\n      denominator: 3\n    }, {\n      numerator: 2,\n      denominator: 3\n    }, {\n      numerator: 1,\n      denominator: 4\n    }, {\n      numerator: 3,\n      denominator: 4\n    }, {\n      numerator: 1,\n      denominator: 5\n    }, {\n      numerator: 2,\n      denominator: 5\n    }, {\n      numerator: 3,\n      denominator: 5\n    }, {\n      numerator: 4,\n      denominator: 5\n    }, {\n      numerator: 1,\n      denominator: 6\n    }, {\n      numerator: 5,\n      denominator: 6\n    }, {\n      numerator: 1,\n      denominator: 8\n    }, {\n      numerator: 3,\n      denominator: 8\n    }, {\n      numerator: 5,\n      denominator: 8\n    }, {\n      numerator: 7,\n      denominator: 8\n    }];\n    return easyFractions[Math.floor(Math.random() * easyFractions.length)];\n  };\n\n  // Spustí novou hru\n  const startNewRound = () => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: {\n        numerator: 0,\n        denominator: 1\n      },\n      score: gameState.score,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  };\n\n  // Resetuje celou hru\n  const resetGame = () => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: {\n        numerator: 0,\n        denominator: 1\n      },\n      score: 0,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  };\n\n  // Zobrazí/skryje nápovědu\n  const toggleHint = () => {\n    setGameState(prev => ({\n      ...prev,\n      showHint: !prev.showHint\n    }));\n  };\n\n  // Řezání pizzy (zvyšuje počet dílků)\n  const cutPizza = () => {\n    if (gameState.gamePhase === 'cutting') {\n      soundEffects.playSliceSound();\n      const newTotalSlices = gameState.totalSlices + 1;\n      setGameState(prev => ({\n        ...prev,\n        totalSlices: newTotalSlices,\n        currentFraction: {\n          ...prev.currentFraction,\n          denominator: newTotalSlices\n        },\n        selectedSlices: new Array(newTotalSlices).fill(false)\n      }));\n    }\n  };\n\n  // Výběr/zrušení výběru dílku pizzy\n  const toggleSlice = index => {\n    if (gameState.gamePhase === 'cutting') {\n      setGameState(prev => ({\n        ...prev,\n        gamePhase: 'selecting'\n      }));\n    }\n    if (gameState.gamePhase === 'selecting') {\n      soundEffects.playSelectSound();\n      const newSelectedSlices = [...gameState.selectedSlices];\n      newSelectedSlices[index] = !newSelectedSlices[index];\n      const selectedCount = newSelectedSlices.filter(Boolean).length;\n      setGameState(prev => ({\n        ...prev,\n        selectedSlices: newSelectedSlices,\n        currentFraction: {\n          numerator: selectedCount,\n          denominator: prev.totalSlices\n        }\n      }));\n    }\n  };\n\n  // Kontrola správnosti odpovědi\n  const checkAnswer = () => {\n    const {\n      targetFraction,\n      currentFraction\n    } = gameState;\n\n    // Zjednodušení zlomků pro porovnání\n    const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);\n    const simplifyFraction = frac => {\n      const divisor = gcd(frac.numerator, frac.denominator);\n      return {\n        numerator: frac.numerator / divisor,\n        denominator: frac.denominator / divisor\n      };\n    };\n    const simplifiedTarget = simplifyFraction(targetFraction);\n    const simplifiedCurrent = simplifyFraction(currentFraction);\n    return simplifiedTarget.numerator === simplifiedCurrent.numerator && simplifiedTarget.denominator === simplifiedCurrent.denominator;\n  };\n\n  // Potvrzení odpovědi\n  const submitAnswer = () => {\n    if (gameState.gamePhase === 'selecting') {\n      const isCorrect = checkAnswer();\n      if (isCorrect) {\n        soundEffects.playSuccessSound();\n        setGameState(prev => ({\n          ...prev,\n          score: prev.score + 1,\n          gamePhase: 'complete',\n          feedback: '🎉 Výborně! Správná odpověď!'\n        }));\n      } else {\n        soundEffects.playErrorSound();\n        // Špatná odpověď - resetuj výběr\n        setGameState(prev => ({\n          ...prev,\n          selectedSlices: new Array(prev.totalSlices).fill(false),\n          currentFraction: {\n            numerator: 0,\n            denominator: prev.totalSlices\n          },\n          feedback: '❌ Zkus to znovu! Zkontroluj si zlomek.'\n        }));\n      }\n    }\n  };\n\n  // Inicializace první hry\n  useEffect(() => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: {\n        numerator: 0,\n        denominator: 1\n      },\n      score: 0,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pizza-game\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"game-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83C\\uDF55 Pizza Zlomky\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"game-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score\",\n          children: [\"Sk\\xF3re: \", gameState.score]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"target-fraction\",\n          children: [\"C\\xEDl: \", gameState.targetFraction.numerator, \"/\", gameState.targetFraction.denominator]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"current-fraction\",\n          children: [\"Tv\\u016Fj zlomek: \", gameState.currentFraction.numerator, \"/\", gameState.currentFraction.denominator]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"game-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"instructions\",\n        children: [gameState.gamePhase === 'cutting' && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\uD83D\\uDD2A Klikni na n\\u016F\\u017E pro \\u0159ez\\xE1n\\xED pizzy na v\\xEDce d\\xEDlk\\u016F!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this), gameState.gamePhase === 'selecting' && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\uD83D\\uDC46 Klikni na d\\xEDlky pizzy pro v\\xFDb\\u011Br spr\\xE1vn\\xE9ho mno\\u017Estv\\xED!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), gameState.gamePhase === 'complete' && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\uD83C\\uDF89 V\\xFDborn\\u011B! Klikni na \\\"Dal\\u0161\\xED kolo\\\" pro pokra\\u010Dov\\xE1n\\xED.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), gameState.feedback && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `feedback ${gameState.feedback.includes('❌') ? 'error' : 'success'}`,\n          children: gameState.feedback\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"game-area\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"knife-section\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"knife-button\",\n            onClick: cutPizza,\n            disabled: gameState.gamePhase !== 'cutting',\n            children: \"\\uD83D\\uDD2A \\u0158ezat pizzu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pizza-container\",\n          children: /*#__PURE__*/_jsxDEV(PizzaSVG, {\n            totalSlices: gameState.totalSlices,\n            selectedSlices: gameState.selectedSlices,\n            onSliceClick: toggleSlice\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls\",\n          children: [gameState.gamePhase === 'selecting' && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"submit-button\",\n            onClick: submitAnswer,\n            children: \"\\u2705 Potvrdit odpov\\u011B\\u010F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), gameState.gamePhase === 'complete' && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"next-round-button\",\n            onClick: startNewRound,\n            children: \"\\u27A1\\uFE0F Dal\\u0161\\xED kolo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(PizzaFractionGame, \"I+9bTiMvUPcNqPB+HSgKlTGIIfg=\");\n_c = PizzaFractionGame;\nexport default PizzaFractionGame;\nvar _c;\n$RefreshReg$(_c, \"PizzaFractionGame\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PizzaSVG", "soundEffects", "jsxDEV", "_jsxDEV", "PizzaFractionGame", "_s", "gameState", "setGameState", "targetFraction", "numerator", "denominator", "currentFraction", "score", "gamePhase", "selectedSlices", "totalSlices", "feedback", "showHint", "generateRandomFraction", "easyFractions", "Math", "floor", "random", "length", "startNewRound", "newFraction", "resetGame", "toggleHint", "prev", "cutPizza", "playSliceSound", "newTotalSlices", "Array", "fill", "toggleSlice", "index", "playSelectSound", "newSelectedSlices", "selectedCount", "filter", "Boolean", "checkAnswer", "gcd", "a", "b", "simplifyFraction", "frac", "divisor", "simplifiedTarget", "simplifiedCurrent", "submitAnswer", "isCorrect", "playSuccessSound", "playErrorSound", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "includes", "onClick", "disabled", "onSliceClick", "_c", "$RefreshReg$"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/PizzaFractionGame.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './PizzaFractionGame.css';\nimport PizzaSVG from './PizzaSVG';\nimport { soundEffects } from './SoundEffects';\n\ninterface Fraction {\n  numerator: number;\n  denominator: number;\n}\n\ninterface GameState {\n  targetFraction: Fraction;\n  currentFraction: Fraction;\n  score: number;\n  gamePhase: 'cutting' | 'selecting' | 'complete';\n  selectedSlices: boolean[];\n  totalSlices: number;\n  feedback: string;\n  showHint: boolean;\n}\n\nconst PizzaFractionGame: React.FC = () => {\n  const [gameState, setGameState] = useState<GameState>({\n    targetFraction: { numerator: 1, denominator: 2 },\n    currentFraction: { numerator: 0, denominator: 1 },\n    score: 0,\n    gamePhase: 'cutting',\n    selectedSlices: [],\n    totalSlices: 1,\n    feedback: '',\n    showHint: false\n  });\n\n  // Generuje náhodný zlomek menší než 1, v<PERSON>dný pro děti\n  const generateRandomFraction = (): Fraction => {\n    // Začneme s jednodu<PERSON><PERSON><PERSON><PERSON> pro mlad<PERSON><PERSON> děti\n    const easyFractions = [\n      { numerator: 1, denominator: 2 },\n      { numerator: 1, denominator: 3 },\n      { numerator: 2, denominator: 3 },\n      { numerator: 1, denominator: 4 },\n      { numerator: 3, denominator: 4 },\n      { numerator: 1, denominator: 5 },\n      { numerator: 2, denominator: 5 },\n      { numerator: 3, denominator: 5 },\n      { numerator: 4, denominator: 5 },\n      { numerator: 1, denominator: 6 },\n      { numerator: 5, denominator: 6 },\n      { numerator: 1, denominator: 8 },\n      { numerator: 3, denominator: 8 },\n      { numerator: 5, denominator: 8 },\n      { numerator: 7, denominator: 8 }\n    ];\n\n    return easyFractions[Math.floor(Math.random() * easyFractions.length)];\n  };\n\n  // Spustí novou hru\n  const startNewRound = () => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: { numerator: 0, denominator: 1 },\n      score: gameState.score,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  };\n\n  // Resetuje celou hru\n  const resetGame = () => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: { numerator: 0, denominator: 1 },\n      score: 0,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  };\n\n  // Zobrazí/skryje nápovědu\n  const toggleHint = () => {\n    setGameState(prev => ({ ...prev, showHint: !prev.showHint }));\n  };\n\n  // Řezání pizzy (zvyšuje počet dílků)\n  const cutPizza = () => {\n    if (gameState.gamePhase === 'cutting') {\n      soundEffects.playSliceSound();\n      const newTotalSlices = gameState.totalSlices + 1;\n      setGameState(prev => ({\n        ...prev,\n        totalSlices: newTotalSlices,\n        currentFraction: { ...prev.currentFraction, denominator: newTotalSlices },\n        selectedSlices: new Array(newTotalSlices).fill(false)\n      }));\n    }\n  };\n\n  // Výběr/zrušení výběru dílku pizzy\n  const toggleSlice = (index: number) => {\n    if (gameState.gamePhase === 'cutting') {\n      setGameState(prev => ({ ...prev, gamePhase: 'selecting' }));\n    }\n\n    if (gameState.gamePhase === 'selecting') {\n      soundEffects.playSelectSound();\n      const newSelectedSlices = [...gameState.selectedSlices];\n      newSelectedSlices[index] = !newSelectedSlices[index];\n      const selectedCount = newSelectedSlices.filter(Boolean).length;\n\n      setGameState(prev => ({\n        ...prev,\n        selectedSlices: newSelectedSlices,\n        currentFraction: { numerator: selectedCount, denominator: prev.totalSlices }\n      }));\n    }\n  };\n\n  // Kontrola správnosti odpovědi\n  const checkAnswer = () => {\n    const { targetFraction, currentFraction } = gameState;\n    \n    // Zjednodušení zlomků pro porovnání\n    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);\n    \n    const simplifyFraction = (frac: Fraction): Fraction => {\n      const divisor = gcd(frac.numerator, frac.denominator);\n      return {\n        numerator: frac.numerator / divisor,\n        denominator: frac.denominator / divisor\n      };\n    };\n    \n    const simplifiedTarget = simplifyFraction(targetFraction);\n    const simplifiedCurrent = simplifyFraction(currentFraction);\n    \n    return simplifiedTarget.numerator === simplifiedCurrent.numerator && \n           simplifiedTarget.denominator === simplifiedCurrent.denominator;\n  };\n\n  // Potvrzení odpovědi\n  const submitAnswer = () => {\n    if (gameState.gamePhase === 'selecting') {\n      const isCorrect = checkAnswer();\n      if (isCorrect) {\n        soundEffects.playSuccessSound();\n        setGameState(prev => ({\n          ...prev,\n          score: prev.score + 1,\n          gamePhase: 'complete',\n          feedback: '🎉 Výborně! Správná odpověď!'\n        }));\n      } else {\n        soundEffects.playErrorSound();\n        // Špatná odpověď - resetuj výběr\n        setGameState(prev => ({\n          ...prev,\n          selectedSlices: new Array(prev.totalSlices).fill(false),\n          currentFraction: { numerator: 0, denominator: prev.totalSlices },\n          feedback: '❌ Zkus to znovu! Zkontroluj si zlomek.'\n        }));\n      }\n    }\n  };\n\n  // Inicializace první hry\n  useEffect(() => {\n    const newFraction = generateRandomFraction();\n    setGameState({\n      targetFraction: newFraction,\n      currentFraction: { numerator: 0, denominator: 1 },\n      score: 0,\n      gamePhase: 'cutting',\n      selectedSlices: [],\n      totalSlices: 1,\n      feedback: '',\n      showHint: false\n    });\n  }, []);\n\n  return (\n    <div className=\"pizza-game\">\n      <header className=\"game-header\">\n        <h1>🍕 Pizza Zlomky</h1>\n        <div className=\"game-info\">\n          <div className=\"score\">Skóre: {gameState.score}</div>\n          <div className=\"target-fraction\">\n            Cíl: {gameState.targetFraction.numerator}/{gameState.targetFraction.denominator}\n          </div>\n          <div className=\"current-fraction\">\n            Tvůj zlomek: {gameState.currentFraction.numerator}/{gameState.currentFraction.denominator}\n          </div>\n        </div>\n      </header>\n\n      <main className=\"game-content\">\n        <div className=\"instructions\">\n          {gameState.gamePhase === 'cutting' && (\n            <p>🔪 Klikni na nůž pro řezání pizzy na více dílků!</p>\n          )}\n          {gameState.gamePhase === 'selecting' && (\n            <p>👆 Klikni na dílky pizzy pro výběr správného množství!</p>\n          )}\n          {gameState.gamePhase === 'complete' && (\n            <p>🎉 Výborně! Klikni na \"Další kolo\" pro pokračování.</p>\n          )}\n          {gameState.feedback && (\n            <div className={`feedback ${gameState.feedback.includes('❌') ? 'error' : 'success'}`}>\n              {gameState.feedback}\n            </div>\n          )}\n        </div>\n\n        <div className=\"game-area\">\n          <div className=\"knife-section\">\n            <button \n              className=\"knife-button\"\n              onClick={cutPizza}\n              disabled={gameState.gamePhase !== 'cutting'}\n            >\n              🔪 Řezat pizzu\n            </button>\n          </div>\n\n          <div className=\"pizza-container\">\n            <PizzaSVG\n              totalSlices={gameState.totalSlices}\n              selectedSlices={gameState.selectedSlices}\n              onSliceClick={toggleSlice}\n            />\n          </div>\n\n          <div className=\"controls\">\n            {gameState.gamePhase === 'selecting' && (\n              <button className=\"submit-button\" onClick={submitAnswer}>\n                ✅ Potvrdit odpověď\n              </button>\n            )}\n            {gameState.gamePhase === 'complete' && (\n              <button className=\"next-round-button\" onClick={startNewRound}>\n                ➡️ Další kolo\n              </button>\n            )}\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default PizzaFractionGame;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,yBAAyB;AAChC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkB9C,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAY;IACpDU,cAAc,EAAE;MAAEC,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;IAChDC,eAAe,EAAE;MAAEF,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;IACjDE,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,SAAS;IACpBC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAgB;IAC7C;IACA,MAAMC,aAAa,GAAG,CACpB;MAAEV,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,EAChC;MAAED,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,CACjC;IAED,OAAOS,aAAa,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,aAAa,CAACI,MAAM,CAAC,CAAC;EACxE,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,WAAW,GAAGP,sBAAsB,CAAC,CAAC;IAC5CX,YAAY,CAAC;MACXC,cAAc,EAAEiB,WAAW;MAC3Bd,eAAe,EAAE;QAAEF,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAE,CAAC;MACjDE,KAAK,EAAEN,SAAS,CAACM,KAAK;MACtBC,SAAS,EAAE,SAAS;MACpBC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMS,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMD,WAAW,GAAGP,sBAAsB,CAAC,CAAC;IAC5CX,YAAY,CAAC;MACXC,cAAc,EAAEiB,WAAW;MAC3Bd,eAAe,EAAE;QAAEF,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAE,CAAC;MACjDE,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,SAAS;MACpBC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMU,UAAU,GAAGA,CAAA,KAAM;IACvBpB,YAAY,CAACqB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEX,QAAQ,EAAE,CAACW,IAAI,CAACX;IAAS,CAAC,CAAC,CAAC;EAC/D,CAAC;;EAED;EACA,MAAMY,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIvB,SAAS,CAACO,SAAS,KAAK,SAAS,EAAE;MACrCZ,YAAY,CAAC6B,cAAc,CAAC,CAAC;MAC7B,MAAMC,cAAc,GAAGzB,SAAS,CAACS,WAAW,GAAG,CAAC;MAChDR,YAAY,CAACqB,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPb,WAAW,EAAEgB,cAAc;QAC3BpB,eAAe,EAAE;UAAE,GAAGiB,IAAI,CAACjB,eAAe;UAAED,WAAW,EAAEqB;QAAe,CAAC;QACzEjB,cAAc,EAAE,IAAIkB,KAAK,CAACD,cAAc,CAAC,CAACE,IAAI,CAAC,KAAK;MACtD,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAIC,KAAa,IAAK;IACrC,IAAI7B,SAAS,CAACO,SAAS,KAAK,SAAS,EAAE;MACrCN,YAAY,CAACqB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEf,SAAS,EAAE;MAAY,CAAC,CAAC,CAAC;IAC7D;IAEA,IAAIP,SAAS,CAACO,SAAS,KAAK,WAAW,EAAE;MACvCZ,YAAY,CAACmC,eAAe,CAAC,CAAC;MAC9B,MAAMC,iBAAiB,GAAG,CAAC,GAAG/B,SAAS,CAACQ,cAAc,CAAC;MACvDuB,iBAAiB,CAACF,KAAK,CAAC,GAAG,CAACE,iBAAiB,CAACF,KAAK,CAAC;MACpD,MAAMG,aAAa,GAAGD,iBAAiB,CAACE,MAAM,CAACC,OAAO,CAAC,CAACjB,MAAM;MAE9DhB,YAAY,CAACqB,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPd,cAAc,EAAEuB,iBAAiB;QACjC1B,eAAe,EAAE;UAAEF,SAAS,EAAE6B,aAAa;UAAE5B,WAAW,EAAEkB,IAAI,CAACb;QAAY;MAC7E,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAM0B,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAM;MAAEjC,cAAc;MAAEG;IAAgB,CAAC,GAAGL,SAAS;;IAErD;IACA,MAAMoC,GAAG,GAAGA,CAACC,CAAS,EAAEC,CAAS,KAAaA,CAAC,KAAK,CAAC,GAAGD,CAAC,GAAGD,GAAG,CAACE,CAAC,EAAED,CAAC,GAAGC,CAAC,CAAC;IAEzE,MAAMC,gBAAgB,GAAIC,IAAc,IAAe;MACrD,MAAMC,OAAO,GAAGL,GAAG,CAACI,IAAI,CAACrC,SAAS,EAAEqC,IAAI,CAACpC,WAAW,CAAC;MACrD,OAAO;QACLD,SAAS,EAAEqC,IAAI,CAACrC,SAAS,GAAGsC,OAAO;QACnCrC,WAAW,EAAEoC,IAAI,CAACpC,WAAW,GAAGqC;MAClC,CAAC;IACH,CAAC;IAED,MAAMC,gBAAgB,GAAGH,gBAAgB,CAACrC,cAAc,CAAC;IACzD,MAAMyC,iBAAiB,GAAGJ,gBAAgB,CAAClC,eAAe,CAAC;IAE3D,OAAOqC,gBAAgB,CAACvC,SAAS,KAAKwC,iBAAiB,CAACxC,SAAS,IAC1DuC,gBAAgB,CAACtC,WAAW,KAAKuC,iBAAiB,CAACvC,WAAW;EACvE,CAAC;;EAED;EACA,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5C,SAAS,CAACO,SAAS,KAAK,WAAW,EAAE;MACvC,MAAMsC,SAAS,GAAGV,WAAW,CAAC,CAAC;MAC/B,IAAIU,SAAS,EAAE;QACblD,YAAY,CAACmD,gBAAgB,CAAC,CAAC;QAC/B7C,YAAY,CAACqB,IAAI,KAAK;UACpB,GAAGA,IAAI;UACPhB,KAAK,EAAEgB,IAAI,CAAChB,KAAK,GAAG,CAAC;UACrBC,SAAS,EAAE,UAAU;UACrBG,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLf,YAAY,CAACoD,cAAc,CAAC,CAAC;QAC7B;QACA9C,YAAY,CAACqB,IAAI,KAAK;UACpB,GAAGA,IAAI;UACPd,cAAc,EAAE,IAAIkB,KAAK,CAACJ,IAAI,CAACb,WAAW,CAAC,CAACkB,IAAI,CAAC,KAAK,CAAC;UACvDtB,eAAe,EAAE;YAAEF,SAAS,EAAE,CAAC;YAAEC,WAAW,EAAEkB,IAAI,CAACb;UAAY,CAAC;UAChEC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;;EAED;EACAjB,SAAS,CAAC,MAAM;IACd,MAAM0B,WAAW,GAAGP,sBAAsB,CAAC,CAAC;IAC5CX,YAAY,CAAC;MACXC,cAAc,EAAEiB,WAAW;MAC3Bd,eAAe,EAAE;QAAEF,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAE,CAAC;MACjDE,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,SAAS;MACpBC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEd,OAAA;IAAKmD,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBpD,OAAA;MAAQmD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC7BpD,OAAA;QAAAoD,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAKmD,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAC,YAAO,EAACjD,SAAS,CAACM,KAAK;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrDxD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UAC1B,EAACjD,SAAS,CAACE,cAAc,CAACC,SAAS,EAAC,GAAC,EAACH,SAAS,CAACE,cAAc,CAACE,WAAW;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,oBACnB,EAACjD,SAAS,CAACK,eAAe,CAACF,SAAS,EAAC,GAAC,EAACH,SAAS,CAACK,eAAe,CAACD,WAAW;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETxD,OAAA;MAAMmD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC5BpD,OAAA;QAAKmD,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1BjD,SAAS,CAACO,SAAS,KAAK,SAAS,iBAChCV,OAAA;UAAAoD,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACvD,EACArD,SAAS,CAACO,SAAS,KAAK,WAAW,iBAClCV,OAAA;UAAAoD,QAAA,EAAG;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC7D,EACArD,SAAS,CAACO,SAAS,KAAK,UAAU,iBACjCV,OAAA;UAAAoD,QAAA,EAAG;QAAmD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC1D,EACArD,SAAS,CAACU,QAAQ,iBACjBb,OAAA;UAAKmD,SAAS,EAAE,YAAYhD,SAAS,CAACU,QAAQ,CAAC4C,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;UAAAL,QAAA,EAClFjD,SAAS,CAACU;QAAQ;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAKmD,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BpD,OAAA;YACEmD,SAAS,EAAC,cAAc;YACxBO,OAAO,EAAEhC,QAAS;YAClBiC,QAAQ,EAAExD,SAAS,CAACO,SAAS,KAAK,SAAU;YAAA0C,QAAA,EAC7C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENxD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BpD,OAAA,CAACH,QAAQ;YACPe,WAAW,EAAET,SAAS,CAACS,WAAY;YACnCD,cAAc,EAAER,SAAS,CAACQ,cAAe;YACzCiD,YAAY,EAAE7B;UAAY;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxD,OAAA;UAAKmD,SAAS,EAAC,UAAU;UAAAC,QAAA,GACtBjD,SAAS,CAACO,SAAS,KAAK,WAAW,iBAClCV,OAAA;YAAQmD,SAAS,EAAC,eAAe;YAACO,OAAO,EAAEX,YAAa;YAAAK,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EACArD,SAAS,CAACO,SAAS,KAAK,UAAU,iBACjCV,OAAA;YAAQmD,SAAS,EAAC,mBAAmB;YAACO,OAAO,EAAErC,aAAc;YAAA+B,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtD,EAAA,CA3OID,iBAA2B;AAAA4D,EAAA,GAA3B5D,iBAA2B;AA6OjC,eAAeA,iBAAiB;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}