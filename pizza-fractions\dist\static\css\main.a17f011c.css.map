{"version": 3, "file": "static/css/main.a17f011c.css", "mappings": "AAAA,KAEE,mIAKF,CAEA,KACE,uEAEF,CCZA,KACE,QAAS,CAET,gBAAiB,CADjB,SAGF,CAEA,OAHE,qBAKF,CAEA,KAME,kCAAmC,CACnC,iCAAkC,CAJlC,yJAEY,CAJZ,QAAS,CACT,SAMF,CCnBA,YAIE,wCAAyC,CAFzC,kEAAuE,CACvE,yBAA0B,CAK1B,qBAAsB,CAFtB,aAAc,CADd,4CAAiD,CAJjD,gBAAiB,CAMjB,YAEF,CAEA,yBACE,GAAK,yBAA6B,CAClC,IAAM,4BAA+B,CACrC,GAAO,yBAA6B,CACtC,CAEA,aAOE,kCAA2B,CAA3B,0BAA2B,CAJ3B,oBAAoC,CACpC,kBAAmB,CAEnB,+BAAyC,CAJzC,kBAAmB,CAGnB,YAAa,CAJb,iBAOF,CAEA,gBAME,kBAAmB,CAFnB,aAAc,CACd,YAAa,CAJb,gBAAiB,CAOjB,QAAS,CADT,sBAAuB,CALvB,eAAkB,CAClB,iCAMF,CAEA,YACE,oBAAqB,CACrB,4CACF,CAEA,gBAGE,kBAAmB,CADnB,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAEA,WACE,YAAa,CAEb,cAAe,CACf,QAAS,CAFT,4BAGF,CAEA,eACE,kBAAmB,CAGnB,kBAAmB,CAGnB,+BAA8C,CAL9C,UAAY,CAIZ,gBAAiB,CADjB,eAAiB,CAFjB,iBAKF,CAEA,OACE,4BAA8B,CAC9B,yCACF,CAEA,iBACE,4BAA8B,CAC9B,yCACF,CAEA,eACE,YAAa,CAIb,cAAe,CAFf,QAAS,CADT,sBAAuB,CAEvB,eAEF,CAEA,2BAEE,iDAAoD,CAEpD,WAAY,CAGZ,kBAAmB,CAEnB,+BAA8C,CAN9C,UAAY,CAKZ,cAAe,CAGf,mBAAoB,CALpB,eAAiB,CAMjB,eAAiB,CAPjB,gBAAiB,CAKjB,uBAGF,CAEA,cACE,iDAAoD,CACpD,+BACF,CAEA,uCAGE,+BAA8C,CAD9C,0BAEF,CAEA,oBACE,+BACF,CAEA,cAGE,gBAAoC,CACpC,kBAAmB,CAInB,+BAAyC,CAFzC,gBAAiB,CACjB,eAAiB,CALjB,kBAAmB,CAGnB,YAAa,CAJb,iBAQF,CAEA,UAME,yBAA2B,CAH3B,kBAAmB,CACnB,gBAAiB,CACjB,eAAiB,CAJjB,eAAgB,CAChB,iBAKF,CAEA,kBACE,iDAAoD,CAEpD,+BAA8C,CAD9C,UAEF,CAEA,gBACE,iDAAoD,CAEpD,+BAA6C,CAD7C,UAEF,CAEA,kBACE,GAAO,SAAU,CAAE,2BAA8B,CACjD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,MAQE,yBAA2B,CAH3B,iDAAoD,CAFpD,kBAAmB,CAInB,+BAA8C,CAD9C,UAAY,CAFZ,gBAAiB,CAHjB,eAAgB,CAChB,iBAOF,CAEA,WAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QACF,CAEA,eACE,YAAa,CAGb,cAAe,CADf,QAAS,CADT,sBAGF,CAEA,cACE,iDAAoD,CAEpD,WAAY,CAGZ,kBAAmB,CAEnB,+BAA6C,CAN7C,UAAY,CAKZ,cAAe,CAGf,mBAAoB,CALpB,gBAAiB,CAMjB,eAAiB,CAPjB,iBAAkB,CAKlB,uBAGF,CAEA,mCAEE,gCAA8C,CAD9C,0BAEF,CAEA,uBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,qBACE,iDAAoD,CAEpD,WAAY,CAGZ,kBAAmB,CAEnB,+BAA8C,CAN9C,UAAY,CAKZ,cAAe,CAGf,mBAAoB,CALpB,gBAAiB,CAMjB,eAAiB,CAPjB,iBAAkB,CAKlB,uBAGF,CAEA,0CAEE,gCAA+C,CAD/C,0BAEF,CAEA,8BAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,iBAIE,gBACF,CAEA,sCAJE,kBAAmB,CAFnB,YAAa,CACb,sBAUF,CALA,qBAIE,8CACF,CAEA,yBAEE,WAAY,CADZ,cAEF,CAEA,iBACE,uBACF,CAEA,uBACE,sBAAuB,CACvB,uBACF,CAMA,kCAEE,iDAAoD,CAEpD,WAAY,CAGZ,kBAAmB,CAEnB,+BAA8C,CAN9C,UAAY,CAKZ,cAAe,CAGf,mBAAoB,CALpB,gBAAiB,CAMjB,eAAiB,CAPjB,iBAAkB,CAKlB,uBAGF,CAEA,8CAGE,gCAA+C,CAD/C,0BAEF,CAGA,yBACE,YACE,YACF,CAEA,gBAEE,qBAAsB,CADtB,cAAe,CAEf,QACF,CAEA,gBAEE,WAAY,CADZ,UAEF,CAEA,WAEE,kBAAmB,CADnB,qBAEF,CAEA,eACE,cAAe,CACf,gBACF,CAEA,cACE,gBAAiB,CACjB,YACF,CAEA,yBAEE,YAAa,CADb,WAEF,CAEA,qEAIE,gBAAiB,CACjB,iBACF,CAEA,2BAEE,eAAiB,CACjB,gBACF,CAEA,eACE,QACF,CACF,CAEA,yBACE,gBAEE,qBAAsB,CADtB,gBAAiB,CAEjB,OACF,CAEA,gBAEE,WAAY,CADZ,UAEF,CAEA,yBAEE,YAAa,CADb,WAEF,CAEA,qEAIE,cAAe,CACf,iBACF,CAEA,cACE,cACF,CACF,CAGA,qBACE,MAAW,kBAAqB,CAChC,IAAM,oBAAuB,CAC/B", "sources": ["index.css", "App.css", "components/PizzaFractionGame.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".App {\n  margin: 0;\n  padding: 0;\n  min-height: 100vh;\n  box-sizing: border-box;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: 'Comic Sans MS', cursive, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n", ".pizza-game {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);\n  background-size: 400% 400%;\n  animation: gradientShift 8s ease infinite;\n  font-family: 'Comic Sans MS', cursive, sans-serif;\n  color: #2c3e50;\n  padding: 20px;\n  box-sizing: border-box;\n}\n\n@keyframes gradientShift {\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n}\n\n.game-header {\n  text-align: center;\n  margin-bottom: 30px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n}\n\n.game-header h1 {\n  font-size: 2.5rem;\n  margin: 0 0 20px 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);\n  color: #e74c3c;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n}\n\n.pizza-chef {\n  display: inline-block;\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));\n}\n\n.pizza-chef img {\n  width: 80px;\n  height: 80px;\n  border-radius: 10px;\n  object-fit: cover;\n}\n\n.game-info {\n  display: flex;\n  justify-content: space-around;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.game-info > div {\n  background: #3498db;\n  color: white;\n  padding: 10px 20px;\n  border-radius: 25px;\n  font-weight: bold;\n  font-size: 1.1rem;\n  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);\n}\n\n.score {\n  background: #27ae60 !important;\n  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3) !important;\n}\n\n.target-fraction {\n  background: #e74c3c !important;\n  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;\n}\n\n.game-controls {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  margin-top: 20px;\n  flex-wrap: wrap;\n}\n\n.hint-button,\n.reset-button {\n  background: linear-gradient(45deg, #9b59b6, #8e44ad);\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  font-size: 0.9rem;\n  border-radius: 20px;\n  cursor: pointer;\n  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);\n  transition: all 0.3s ease;\n  font-family: inherit;\n  font-weight: bold;\n}\n\n.reset-button {\n  background: linear-gradient(45deg, #e67e22, #d35400);\n  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);\n}\n\n.hint-button:hover,\n.reset-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);\n}\n\n.reset-button:hover {\n  box-shadow: 0 6px 20px rgba(230, 126, 34, 0.4);\n}\n\n.instructions {\n  text-align: center;\n  margin-bottom: 30px;\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 15px;\n  padding: 15px;\n  font-size: 1.3rem;\n  font-weight: bold;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.feedback {\n  margin-top: 15px;\n  padding: 10px 20px;\n  border-radius: 25px;\n  font-size: 1.2rem;\n  font-weight: bold;\n  animation: fadeIn 0.5s ease;\n}\n\n.feedback.success {\n  background: linear-gradient(45deg, #2ecc71, #27ae60);\n  color: white;\n  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);\n}\n\n.feedback.error {\n  background: linear-gradient(45deg, #e74c3c, #c0392b);\n  color: white;\n  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(-10px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n.hint {\n  margin-top: 15px;\n  padding: 15px 20px;\n  border-radius: 15px;\n  font-size: 1.1rem;\n  background: linear-gradient(45deg, #f39c12, #e67e22);\n  color: white;\n  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);\n  animation: fadeIn 0.5s ease;\n}\n\n.game-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 30px;\n}\n\n.knife-section {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.knife-button {\n  background: linear-gradient(45deg, #ff6b6b, #ee5a52);\n  color: white;\n  border: none;\n  padding: 15px 30px;\n  font-size: 1.5rem;\n  border-radius: 50px;\n  cursor: pointer;\n  box-shadow: 0 8px 25px rgba(238, 90, 82, 0.4);\n  transition: all 0.3s ease;\n  font-family: inherit;\n  font-weight: bold;\n}\n\n.knife-button:hover:not(:disabled) {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 35px rgba(238, 90, 82, 0.6);\n}\n\n.knife-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.done-cutting-button {\n  background: linear-gradient(45deg, #4ecdc4, #44a08d);\n  color: white;\n  border: none;\n  padding: 15px 30px;\n  font-size: 1.5rem;\n  border-radius: 50px;\n  cursor: pointer;\n  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);\n  transition: all 0.3s ease;\n  font-family: inherit;\n  font-weight: bold;\n}\n\n.done-cutting-button:hover:not(:disabled) {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 35px rgba(78, 205, 196, 0.6);\n}\n\n.done-cutting-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.pizza-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n}\n\n.pizza-svg-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));\n}\n\n.pizza-svg-container svg {\n  max-width: 100%;\n  height: auto;\n}\n\n.pizza-slice-svg {\n  transition: all 0.3s ease;\n}\n\n.pizza-slice-svg:hover {\n  filter: brightness(1.1);\n  transform-origin: center;\n}\n\n\n\n/* .controls sekce odstraněna - tlačítka jsou nyní v knife-section */\n\n.submit-button,\n.next-round-button {\n  background: linear-gradient(45deg, #2ecc71, #27ae60);\n  color: white;\n  border: none;\n  padding: 15px 30px;\n  font-size: 1.5rem;\n  border-radius: 50px;\n  cursor: pointer;\n  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);\n  transition: all 0.3s ease;\n  font-family: inherit;\n  font-weight: bold;\n}\n\n.submit-button:hover,\n.next-round-button:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 35px rgba(46, 204, 113, 0.6);\n}\n\n/* Responzivní design pro mobilní zařízení */\n@media (max-width: 768px) {\n  .pizza-game {\n    padding: 10px;\n  }\n  \n  .game-header h1 {\n    font-size: 2rem;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .pizza-chef img {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .game-info {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .game-info > div {\n    font-size: 1rem;\n    padding: 8px 16px;\n  }\n  \n  .instructions {\n    font-size: 1.1rem;\n    padding: 12px;\n  }\n  \n  .pizza-svg-container svg {\n    width: 250px;\n    height: 250px;\n  }\n  \n  .knife-button,\n  .done-cutting-button,\n  .submit-button,\n  .next-round-button {\n    font-size: 1.2rem;\n    padding: 12px 24px;\n  }\n\n  .hint-button,\n  .reset-button {\n    font-size: 0.8rem;\n    padding: 6px 12px;\n  }\n\n  .game-controls {\n    gap: 10px;\n  }\n}\n\n@media (max-width: 480px) {\n  .game-header h1 {\n    font-size: 1.8rem;\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .pizza-chef img {\n    width: 50px;\n    height: 50px;\n  }\n\n  .pizza-svg-container svg {\n    width: 200px;\n    height: 200px;\n  }\n\n  .knife-button,\n  .done-cutting-button,\n  .submit-button,\n  .next-round-button {\n    font-size: 1rem;\n    padding: 10px 20px;\n  }\n\n  .instructions {\n    font-size: 1rem;\n  }\n}\n\n/* Animace pro úspěšné dokončení */\n@keyframes celebrate {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n}\n"], "names": [], "sourceRoot": ""}