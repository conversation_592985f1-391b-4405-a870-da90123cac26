{"ast": null, "code": "var _jsxFileName = \"C:\\\\Work\\\\new_AI\\\\PizzaFraction\\\\pizza-fractions\\\\src\\\\components\\\\PizzaSVG.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PizzaSVG = ({\n  totalSlices,\n  selectedSlices,\n  onSliceClick\n}) => {\n  const centerX = 150;\n  const centerY = 150;\n  const radius = 130;\n  const createSlicePath = index => {\n    const anglePerSlice = 2 * Math.PI / totalSlices;\n    const startAngle = index * anglePerSlice - Math.PI / 2; // Start from top\n    const endAngle = (index + 1) * anglePerSlice - Math.PI / 2;\n    const x1 = centerX + radius * Math.cos(startAngle);\n    const y1 = centerY + radius * Math.sin(startAngle);\n    const x2 = centerX + radius * Math.cos(endAngle);\n    const y2 = centerY + radius * Math.sin(endAngle);\n    const largeArcFlag = anglePerSlice > Math.PI ? 1 : 0;\n    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pizza-svg-container\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"300\",\n      height: \"300\",\n      viewBox: \"0 0 300 300\",\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: centerX,\n        cy: centerY,\n        r: radius + 8,\n        fill: \"#d68910\",\n        stroke: \"#b7950b\",\n        strokeWidth: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: centerX,\n        cy: centerY,\n        r: radius,\n        fill: \"#f39c12\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), Array.from({\n        length: totalSlices\n      }, (_, index) => /*#__PURE__*/_jsxDEV(\"g\", {\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: createSlicePath(index),\n          fill: selectedSlices[index] ? \"#2ecc71\" : \"#f39c12\",\n          stroke: \"#d68910\",\n          strokeWidth: \"2\",\n          className: \"pizza-slice-svg\",\n          onClick: () => onSliceClick(index),\n          style: {\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this), totalSlices <= 8 && /*#__PURE__*/_jsxDEV(\"g\", {\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: centerX + radius * 0.4 * Math.cos((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2),\n            cy: centerY + radius * 0.4 * Math.sin((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2),\n            r: \"4\",\n            fill: \"#e74c3c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: centerX + radius * 0.6 * Math.cos((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2),\n            cy: centerY + radius * 0.6 * Math.sin((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2),\n            r: \"3\",\n            fill: \"#27ae60\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: centerX + radius * 0.5 * Math.cos((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2),\n            cy: centerY + radius * 0.5 * Math.sin((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2),\n            r: \"3\",\n            fill: \"#f1c40f\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this)), Array.from({\n        length: totalSlices\n      }, (_, index) => {\n        const angle = index * (2 * Math.PI) / totalSlices - Math.PI / 2;\n        const x = centerX + radius * Math.cos(angle);\n        const y = centerY + radius * Math.sin(angle);\n        return /*#__PURE__*/_jsxDEV(\"line\", {\n          x1: centerX,\n          y1: centerY,\n          x2: x,\n          y2: y,\n          stroke: \"#d68910\",\n          strokeWidth: \"3\"\n        }, `divider-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_c = PizzaSVG;\nexport default PizzaSVG;\nvar _c;\n$RefreshReg$(_c, \"PizzaSVG\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PizzaSVG", "totalSlices", "selectedSlices", "onSliceClick", "centerX", "centerY", "radius", "createSlicePath", "index", "anglePerSlice", "Math", "PI", "startAngle", "endAngle", "x1", "cos", "y1", "sin", "x2", "y2", "largeArcFlag", "className", "children", "width", "height", "viewBox", "cx", "cy", "r", "fill", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "from", "length", "_", "d", "onClick", "style", "cursor", "angle", "x", "y", "_c", "$RefreshReg$"], "sources": ["C:/Work/new_AI/PizzaFraction/pizza-fractions/src/components/PizzaSVG.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface PizzaSVGProps {\n  totalSlices: number;\n  selectedSlices: boolean[];\n  onSliceClick: (index: number) => void;\n}\n\nconst PizzaSVG: React.FC<PizzaSVGProps> = ({ totalSlices, selectedSlices, onSliceClick }) => {\n  const centerX = 150;\n  const centerY = 150;\n  const radius = 130;\n\n  const createSlicePath = (index: number) => {\n    const anglePerSlice = (2 * Math.PI) / totalSlices;\n    const startAngle = index * anglePerSlice - Math.PI / 2; // Start from top\n    const endAngle = (index + 1) * anglePerSlice - Math.PI / 2;\n\n    const x1 = centerX + radius * Math.cos(startAngle);\n    const y1 = centerY + radius * Math.sin(startAngle);\n    const x2 = centerX + radius * Math.cos(endAngle);\n    const y2 = centerY + radius * Math.sin(endAngle);\n\n    const largeArcFlag = anglePerSlice > Math.PI ? 1 : 0;\n\n    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;\n  };\n\n  return (\n    <div className=\"pizza-svg-container\">\n      <svg width=\"300\" height=\"300\" viewBox=\"0 0 300 300\">\n        {/* Pizza base */}\n        <circle\n          cx={centerX}\n          cy={centerY}\n          r={radius + 8}\n          fill=\"#d68910\"\n          stroke=\"#b7950b\"\n          strokeWidth=\"4\"\n        />\n        <circle\n          cx={centerX}\n          cy={centerY}\n          r={radius}\n          fill=\"#f39c12\"\n        />\n        \n        {/* Pizza slices */}\n        {Array.from({ length: totalSlices }, (_, index) => (\n          <g key={index}>\n            <path\n              d={createSlicePath(index)}\n              fill={selectedSlices[index] ? \"#2ecc71\" : \"#f39c12\"}\n              stroke=\"#d68910\"\n              strokeWidth=\"2\"\n              className=\"pizza-slice-svg\"\n              onClick={() => onSliceClick(index)}\n              style={{ cursor: 'pointer' }}\n            />\n            \n            {/* Pizza toppings */}\n            {totalSlices <= 8 && (\n              <g>\n                <circle\n                  cx={centerX + (radius * 0.4) * Math.cos((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  cy={centerY + (radius * 0.4) * Math.sin((index + 0.3) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  r=\"4\"\n                  fill=\"#e74c3c\"\n                />\n                <circle\n                  cx={centerX + (radius * 0.6) * Math.cos((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  cy={centerY + (radius * 0.6) * Math.sin((index + 0.7) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  r=\"3\"\n                  fill=\"#27ae60\"\n                />\n                <circle\n                  cx={centerX + (radius * 0.5) * Math.cos((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  cy={centerY + (radius * 0.5) * Math.sin((index + 0.5) * (2 * Math.PI) / totalSlices - Math.PI / 2)}\n                  r=\"3\"\n                  fill=\"#f1c40f\"\n                />\n              </g>\n            )}\n          </g>\n        ))}\n        \n        {/* Slice dividers */}\n        {Array.from({ length: totalSlices }, (_, index) => {\n          const angle = index * (2 * Math.PI) / totalSlices - Math.PI / 2;\n          const x = centerX + radius * Math.cos(angle);\n          const y = centerY + radius * Math.sin(angle);\n          \n          return (\n            <line\n              key={`divider-${index}`}\n              x1={centerX}\n              y1={centerY}\n              x2={x}\n              y2={y}\n              stroke=\"#d68910\"\n              strokeWidth=\"3\"\n            />\n          );\n        })}\n      </svg>\n    </div>\n  );\n};\n\nexport default PizzaSVG;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1B,MAAMC,QAAiC,GAAGA,CAAC;EAAEC,WAAW;EAAEC,cAAc;EAAEC;AAAa,CAAC,KAAK;EAC3F,MAAMC,OAAO,GAAG,GAAG;EACnB,MAAMC,OAAO,GAAG,GAAG;EACnB,MAAMC,MAAM,GAAG,GAAG;EAElB,MAAMC,eAAe,GAAIC,KAAa,IAAK;IACzC,MAAMC,aAAa,GAAI,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAIV,WAAW;IACjD,MAAMW,UAAU,GAAGJ,KAAK,GAAGC,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;IACxD,MAAME,QAAQ,GAAG,CAACL,KAAK,GAAG,CAAC,IAAIC,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IAE1D,MAAMG,EAAE,GAAGV,OAAO,GAAGE,MAAM,GAAGI,IAAI,CAACK,GAAG,CAACH,UAAU,CAAC;IAClD,MAAMI,EAAE,GAAGX,OAAO,GAAGC,MAAM,GAAGI,IAAI,CAACO,GAAG,CAACL,UAAU,CAAC;IAClD,MAAMM,EAAE,GAAGd,OAAO,GAAGE,MAAM,GAAGI,IAAI,CAACK,GAAG,CAACF,QAAQ,CAAC;IAChD,MAAMM,EAAE,GAAGd,OAAO,GAAGC,MAAM,GAAGI,IAAI,CAACO,GAAG,CAACJ,QAAQ,CAAC;IAEhD,MAAMO,YAAY,GAAGX,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC;IAEpD,OAAO,KAAKP,OAAO,IAAIC,OAAO,MAAMS,EAAE,IAAIE,EAAE,MAAMV,MAAM,IAAIA,MAAM,MAAMc,YAAY,MAAMF,EAAE,IAAIC,EAAE,IAAI;EACxG,CAAC;EAED,oBACEpB,OAAA;IAAKsB,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClCvB,OAAA;MAAKwB,KAAK,EAAC,KAAK;MAACC,MAAM,EAAC,KAAK;MAACC,OAAO,EAAC,aAAa;MAAAH,QAAA,gBAEjDvB,OAAA;QACE2B,EAAE,EAAEtB,OAAQ;QACZuB,EAAE,EAAEtB,OAAQ;QACZuB,CAAC,EAAEtB,MAAM,GAAG,CAAE;QACduB,IAAI,EAAC,SAAS;QACdC,MAAM,EAAC,SAAS;QAChBC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACFpC,OAAA;QACE2B,EAAE,EAAEtB,OAAQ;QACZuB,EAAE,EAAEtB,OAAQ;QACZuB,CAAC,EAAEtB,MAAO;QACVuB,IAAI,EAAC;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,EAGDC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAErC;MAAY,CAAC,EAAE,CAACsC,CAAC,EAAE/B,KAAK,kBAC5CT,OAAA;QAAAuB,QAAA,gBACEvB,OAAA;UACEyC,CAAC,EAAEjC,eAAe,CAACC,KAAK,CAAE;UAC1BqB,IAAI,EAAE3B,cAAc,CAACM,KAAK,CAAC,GAAG,SAAS,GAAG,SAAU;UACpDsB,MAAM,EAAC,SAAS;UAChBC,WAAW,EAAC,GAAG;UACfV,SAAS,EAAC,iBAAiB;UAC3BoB,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAACK,KAAK,CAAE;UACnCkC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EAGDlC,WAAW,IAAI,CAAC,iBACfF,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YACE2B,EAAE,EAAEtB,OAAO,GAAIE,MAAM,GAAG,GAAG,GAAII,IAAI,CAACK,GAAG,CAAC,CAACP,KAAK,GAAG,GAAG,KAAK,CAAC,GAAGE,IAAI,CAACC,EAAE,CAAC,GAAGV,WAAW,GAAGS,IAAI,CAACC,EAAE,GAAG,CAAC,CAAE;YACnGgB,EAAE,EAAEtB,OAAO,GAAIC,MAAM,GAAG,GAAG,GAAII,IAAI,CAACO,GAAG,CAAC,CAACT,KAAK,GAAG,GAAG,KAAK,CAAC,GAAGE,IAAI,CAACC,EAAE,CAAC,GAAGV,WAAW,GAAGS,IAAI,CAACC,EAAE,GAAG,CAAC,CAAE;YACnGiB,CAAC,EAAC,GAAG;YACLC,IAAI,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACFpC,OAAA;YACE2B,EAAE,EAAEtB,OAAO,GAAIE,MAAM,GAAG,GAAG,GAAII,IAAI,CAACK,GAAG,CAAC,CAACP,KAAK,GAAG,GAAG,KAAK,CAAC,GAAGE,IAAI,CAACC,EAAE,CAAC,GAAGV,WAAW,GAAGS,IAAI,CAACC,EAAE,GAAG,CAAC,CAAE;YACnGgB,EAAE,EAAEtB,OAAO,GAAIC,MAAM,GAAG,GAAG,GAAII,IAAI,CAACO,GAAG,CAAC,CAACT,KAAK,GAAG,GAAG,KAAK,CAAC,GAAGE,IAAI,CAACC,EAAE,CAAC,GAAGV,WAAW,GAAGS,IAAI,CAACC,EAAE,GAAG,CAAC,CAAE;YACnGiB,CAAC,EAAC,GAAG;YACLC,IAAI,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACFpC,OAAA;YACE2B,EAAE,EAAEtB,OAAO,GAAIE,MAAM,GAAG,GAAG,GAAII,IAAI,CAACK,GAAG,CAAC,CAACP,KAAK,GAAG,GAAG,KAAK,CAAC,GAAGE,IAAI,CAACC,EAAE,CAAC,GAAGV,WAAW,GAAGS,IAAI,CAACC,EAAE,GAAG,CAAC,CAAE;YACnGgB,EAAE,EAAEtB,OAAO,GAAIC,MAAM,GAAG,GAAG,GAAII,IAAI,CAACO,GAAG,CAAC,CAACT,KAAK,GAAG,GAAG,KAAK,CAAC,GAAGE,IAAI,CAACC,EAAE,CAAC,GAAGV,WAAW,GAAGS,IAAI,CAACC,EAAE,GAAG,CAAC,CAAE;YACnGiB,CAAC,EAAC,GAAG;YACLC,IAAI,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACJ;MAAA,GAjCK3B,KAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkCV,CACJ,CAAC,EAGDC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAErC;MAAY,CAAC,EAAE,CAACsC,CAAC,EAAE/B,KAAK,KAAK;QACjD,MAAMoC,KAAK,GAAGpC,KAAK,IAAI,CAAC,GAAGE,IAAI,CAACC,EAAE,CAAC,GAAGV,WAAW,GAAGS,IAAI,CAACC,EAAE,GAAG,CAAC;QAC/D,MAAMkC,CAAC,GAAGzC,OAAO,GAAGE,MAAM,GAAGI,IAAI,CAACK,GAAG,CAAC6B,KAAK,CAAC;QAC5C,MAAME,CAAC,GAAGzC,OAAO,GAAGC,MAAM,GAAGI,IAAI,CAACO,GAAG,CAAC2B,KAAK,CAAC;QAE5C,oBACE7C,OAAA;UAEEe,EAAE,EAAEV,OAAQ;UACZY,EAAE,EAAEX,OAAQ;UACZa,EAAE,EAAE2B,CAAE;UACN1B,EAAE,EAAE2B,CAAE;UACNhB,MAAM,EAAC,SAAS;UAChBC,WAAW,EAAC;QAAG,GANV,WAAWvB,KAAK,EAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOxB,CAAC;MAEN,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GAnGI/C,QAAiC;AAqGvC,eAAeA,QAAQ;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}